<?php

namespace Drupal\search_api_meilisearch\EventSubscriber;

use <PERSON><PERSON>al\search_api\Event\IndexingItemsEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

/**
 * Checks if index fields use reserved machine name on indexing.
 */
class IndexingItemsSubscriber implements EventSubscriberInterface {

  /**
   * Checks if index fields use reserved machine name and prevents indexing.
   *
   * @param \Drupal\search_api\Event\IndexingItemsEvent $event
   *   The indexing items event.
   *
   * @throws \Exception
   */
  public function onIndexingItemsEvent(IndexingItemsEvent $event): void {
    $index = $event->getIndex();
    $fieldSettings = $index->getFields();
    foreach ($fieldSettings as $id => $field) {
      if ($id === 'id') {
        throw new \Exception(sprintf(
          '%s field uses machine name "id" which is reserved for Meilisearch document id.',
          $field->getLabel()
        ));
      }
    }
  }

  /**
   * {@inheritdoc}
   */
  public static function getSubscribedEvents(): array {
    return [
      'search_api.indexing_items' => 'onIndexingItemsEvent',
    ];
  }

}
