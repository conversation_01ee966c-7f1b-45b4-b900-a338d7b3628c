<?php

namespace Drupal\search_api_meilisearch\Backend;

use <PERSON><PERSON><PERSON>\search_api\Item\ItemInterface;

/**
 * Provides an interface for retrieving backend's special fields.
 *
 * Backend plugin base implements getSpecialFields() method but it is marked as
 * protected so the fields cannot be retrieved outside the class. This interface
 * exposes a method that enables retrieving the special fields elsewhere.
 *
 * @see \Drupal\search_api\Backend\BackendPluginBase::getSpecialFields
 */
interface SpecialFieldsInterface {

  /**
   * Retrieves backend's special fields of an item.
   *
   * @param \Drupal\search_api\Item\ItemInterface $item
   *   The item to use for values.
   *
   * @return \Drupal\search_api\Item\Field[]
   *   A list of special fields.
   */
  public function getItemSpecialFields(ItemInterface $item): array;

}
