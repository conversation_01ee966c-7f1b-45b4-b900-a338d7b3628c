<?php

namespace Dr<PERSON>al\search_api_meilisearch\Client;

use <PERSON><PERSON><PERSON>\search_api_meilisearch\Api\MeilisearchApiException;
use <PERSON><PERSON><PERSON>\search_api_meilisearch\Client\Client as HttpAdapter;
use <PERSON>lisearch\Client;

/**
 * The Search API Meilisearch client factory.
 *
 * @package Drupal\search_api_meilisearch\Client
 */
class MeilisearchClientFactory implements MeilisearchClientFactoryInterface {

  /**
   * {@inheritDoc}
   *
   * @throws \Drupal\search_api_meilisearch\Api\MeilisearchApiException
   */
  public function getInstance(string $url, ?string $key = NULL): Client {
    try {
      return new Client($url, $key, new HttpAdapter());
    }
    catch (\Exception $e) {
      throw new MeilisearchApiException($e->getMessage(), $e->getCode(), $e);
    }
  }

}
