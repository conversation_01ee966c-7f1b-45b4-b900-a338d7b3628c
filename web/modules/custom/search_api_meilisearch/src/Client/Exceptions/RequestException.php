<?php

namespace Drupal\search_api_meilisearch\Client\Exceptions;

use Psr\Http\Client\RequestExceptionInterface;
use Psr\Http\Message\RequestInterface;

/**
 * Thrown when an error is encountered in the client.
 */
class RequestException extends ClientException implements RequestExceptionInterface {

  /**
   * The request that caused the exception.
   *
   * @var \Psr\Http\Message\RequestInterface
   */
  private RequestInterface $request;

  /**
   * NetworkException constructor.
   */
  public function __construct(string $message = "", int $code = 0, ?\Throwable $previous = NULL, ?RequestInterface $request = NULL) {
    parent::__construct($message, $code, $previous);
    $this->request = $request;
  }

  /**
   * Returns the request that caused the exception.
   *
   * @return \Psr\Http\Message\RequestInterface
   *   The request that caused the exception.
   */
  public function getRequest(): RequestInterface {
    return $this->request;
  }

}
