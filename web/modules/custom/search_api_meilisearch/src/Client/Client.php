<?php

namespace Dr<PERSON>al\search_api_meilisearch\Client;

use <PERSON><PERSON><PERSON>\search_api_meilisearch\Client\Exceptions\ClientException;
use <PERSON><PERSON>al\search_api_meilisearch\Client\Exceptions\NetworkException;
use Guz<PERSON><PERSON>ttp\Client as GuzzleClient;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Exception\TransferException;
use Psr\Http\Client\ClientInterface;
use Psr\Http\Message\RequestInterface;
use Psr\Http\Message\ResponseInterface;

/**
 * Guzzle client adapter.
 */
class Client implements ClientInterface {

  /**
   * The Guzzle client.
   *
   * @var \GuzzleHttp\Client
   */
  protected GuzzleClient $client;

  /**
   * Client adapter constructor.
   *
   * @param array $config
   *   The configuration array.
   */
  public function __construct(array $config = []) {
    $config['http_errors'] = FALSE;
    $this->client = new GuzzleClient($config);
  }

  /**
   * {@inheritdoc}
   */
  public function sendRequest(RequestInterface $request): ResponseInterface {
    try {
      return $this->client->send($request);
    }
    catch (RequestException $e) {
      throw new NetworkException('Request exception', 0, $e, $request);
    }
    catch (TransferException $e) {
      throw new NetworkException('Transfer exception', 0, $e, $request);
    }
    catch (GuzzleException $e) {
      throw new ClientException('Client exception', 0, $e);
    }
  }

}
