<?php

namespace Drupal\search_api_meilisearch\Client;

use <PERSON><PERSON>ear<PERSON>\Client;

/**
 * Interface MeiliClientFactoryInterface.
 *
 * @package Drupal\search_api_meilisearch\Client
 */
interface MeilisearchClientFactoryInterface {

  /**
   * Returns an instance of the Meilisearch Client.
   *
   * @param string $url
   *   The URL of the server.
   * @param string|null $key
   *   The master key of the server.
   *
   * @return \MeiliSearch\Client
   *   The instance of the Client.
   *
   * @throws \Drupal\search_api_meilisearch\Api\MeilisearchApiException
   */
  public function getInstance(string $url, ?string $key = NULL): Client;

}
