<?php

namespace Drupal\search_api_meilisearch\Api;

use Mei<PERSON>ear<PERSON>\Client;
use Meilisearch\Contracts\DocumentsQuery;
use Meilisearch\Contracts\DocumentsResults;
use Meilisearch\Contracts\IndexesResults;
use Meilisear<PERSON>\Contracts\KeysResults;
use Meilisearch\Endpoints\Indexes;
use Meilisearch\Search\FacetSearchResult;

/**
 * Interface for interacting with the Meilisearch API.
 */
interface MeilisearchApiServiceInterface {

  /**
   * Returns the master key.
   *
   * @return string
   *   The master key.
   */
  public function getMasterKey(): string;

  /**
   * Sets the master key.
   *
   * @param string $key
   *   The master key.
   */
  public function setMasterKey(string $key);

  /**
   * Returns the URL of the server.
   *
   * @return string
   *   The URL of the server.
   */
  public function getUrl(): string;

  /**
   * Sets the server's URL.
   *
   * @param string $url
   *   A URL of the server.
   */
  public function setUrl(string $url);

  /**
   * Provides the getter for the connection instance.
   *
   * @return \MeiliSearch\Client
   *   The Meilisearch client.
   *
   * @throws \Drupal\search_api_meilisearch\Api\MeilisearchApiException
   */
  public function connection(): Client;

  /**
   * Searches the string on the specified index.
   *
   * @param string $indexName
   *   The name of the index to perform the search on.
   * @param string $query
   *   A query string to perform the search on.
   * @param array $options
   *   An array of options for Meilisearch API.
   *
   * @return array|\MeiliSearch\Search\SearchResult
   *   An array of search results.
   *
   * @throws \Drupal\search_api_meilisearch\Api\MeilisearchApiException
   *   Exception on an API call failure.
   *
   * @see https://www.meilisearch.com/docs/reference/api/search#search-in-an-index-with-post
   */
  public function search(string $indexName, string $query, array $options = []);

  /**
   * Gets the index information.
   *
   * @param string $indexName
   *   The name of the index.
   *
   * @return \MeiliSearch\Endpoints\Indexes
   *   Returns the index information.
   *
   * @throws \Drupal\search_api_meilisearch\Api\MeilisearchApiException
   *   Exception on an API call failure.
   *
   * @see https://www.meilisearch.com/docs/reference/api/indexes#get-one-index
   */
  public function getIndex(string $indexName): Indexes;

  /**
   * Creates an index with specified attributes.
   *
   * @param array|string $attributes
   *   The name of the index or an array of attributes.
   *
   * @return array
   *   Returns the index array.
   *
   * @throws \Drupal\search_api_meilisearch\Api\MeilisearchApiException
   *   Exception on an API call failure.
   *
   * @see https://www.meilisearch.com/docs/reference/api/indexes#create-an-index
   */
  public function createIndex($attributes): array;

  /**
   * Removes an index with the specified name.
   *
   * @param string $indexName
   *   The name of the index to remove.
   *
   * @return bool
   *   Returns TRUE on success or FALSE on failure.
   *
   * @throws \Drupal\search_api_meilisearch\Api\MeilisearchApiException
   *   Exception on an API call failure.
   *
   * @see https://www.meilisearch.com/docs/reference/api/indexes#delete-an-index
   */
  public function removeIndex(string $indexName): bool;

  /**
   * Lists all indexes.
   *
   * @return \MeiliSearch\Contracts\IndexesResults
   *   Data with all the indexes.
   *
   * @throws \Drupal\search_api_meilisearch\Api\MeilisearchApiException
   *   Exception on an API call failure.
   *
   * @see https://www.meilisearch.com/docs/reference/api/indexes#list-all-indexes
   */
  public function listIndexes(): IndexesResults;

  /**
   * Adds documents to the index.
   *
   * @param string $indexName
   *   The name of the index.
   * @param array $documents
   *   An array of documents you want to add to index.
   *
   * @return array
   *   An update id.
   *
   * @throws \Drupal\search_api\SearchApiException
   *   Exception on an API call failure.
   *
   * @see https://www.meilisearch.com/docs/reference/api/documents#add-or-update-documents
   */
  public function addDocuments(string $indexName, array $documents): array;

  /**
   * Gets a specific document from an index.
   *
   * @param string $indexName
   *   The name of the index.
   * @param string $id
   *   An id of the document.
   *
   * @return array
   *   Document's data.
   *
   * @throws \Drupal\search_api_meilisearch\Api\MeilisearchApiException
   *   Exception on an API call failure.
   *
   * @see https://www.meilisearch.com/docs/reference/api/documents#get-one-document
   */
  public function getDocument(string $indexName, string $id): array;

  /**
   * Retrieves multiple documents from an index.
   *
   * @param string $indexName
   *   The name of the index.
   * @param \MeiliSearch\Contracts\DocumentsQuery|null $options
   *   Array of options to get the documents by.
   *
   * @return \MeiliSearch\Contracts\DocumentsResults
   *   List of documents from the specified index.
   *
   * @throws \Drupal\search_api_meilisearch\Api\MeilisearchApiException
   *   Exception on an API call failure.
   *
   * @see https://www.meilisearch.com/docs/reference/api/documents#get-documents-with-post
   * @example $options = ['offset' => 10 , 'limit' => 20]
   */
  public function getDocuments(string $indexName, ?DocumentsQuery $options = NULL): DocumentsResults;

  /**
   * Deletes a single document.
   *
   * @param string $indexName
   *   The name of the index.
   * @param string $id
   *   The id of the document to delete.
   *
   * @return array
   *   An update id.
   *
   * @throws \Drupal\search_api_meilisearch\Api\MeilisearchApiException
   *   Exception on an API call failure.
   *
   * @see https://www.meilisearch.com/docs/reference/api/documents#delete-one-document
   */
  public function deleteDocument(string $indexName, string $id): array;

  /**
   * Deletes multiple documents.
   *
   * @param string $indexName
   *   The name of the index.
   * @param array $ids
   *   An array of document's ids.
   *
   * @return array
   *   An update id.
   *
   * @throws \Drupal\search_api_meilisearch\Api\MeilisearchApiException
   *   Exception on an API call failure.
   *
   * @see https://www.meilisearch.com/docs/reference/api/documents#delete-documents-by-batch
   */
  public function deleteDocuments(string $indexName, array $ids): array;

  /**
   * Deletes all documents for a specific index.
   *
   * @param string $indexName
   *   The name of the index from which to delete documents.
   *
   * @return array
   *   An update id.
   *
   * @throws \Drupal\search_api_meilisearch\Api\MeilisearchApiException
   *   Exception on an API call failure.
   *
   * @see https://www.meilisearch.com/docs/reference/api/documents#delete-all-documents
   */
  public function deleteAllDocuments(string $indexName): array;

  /**
   * Sets the filterable attributes on an index.
   *
   * @param string $indexName
   *   The name of the index.
   * @param string[] $fields
   *   An array of fields.
   *
   * @return array
   *   Async task data.
   *
   * @throws \Drupal\search_api_meilisearch\Api\MeilisearchApiException
   *   Exception on an API call failure.
   *
   * @see https://www.meilisearch.com/docs/reference/api/settings#update-filterable-attributes
   */
  public function setFilterableAttributes(string $indexName, array $fields): array;

  /**
   * Gets the filterable attributes of an index.
   *
   * @param string $indexName
   *   The name of the index.
   *
   * @return string[]
   *   An array of settings currently set on the index.
   *
   * @throws \Drupal\search_api_meilisearch\Api\MeilisearchApiException
   *   Exception on an API call failure.
   *
   * @see https://www.meilisearch.com/docs/reference/api/settings#get-filterable-attributes
   */
  public function getFilterableAttributes(string $indexName): array;

  /**
   * Sets the settings on an index.
   *
   * @param string $indexName
   *   The name of the index.
   * @param array $settings
   *   An array of index settings.
   *
   * @return array
   *   Async task data.
   *
   * @throws \Drupal\search_api_meilisearch\Api\MeilisearchApiException
   *   Exception on an API call failure.
   *
   * @see https://www.meilisearch.com/docs/reference/api/settings#update-settings
   */
  public function setSettings(string $indexName, array $settings): array;

  /**
   * Gets the settings of an index.
   *
   * @param string $indexName
   *   The name of the index.
   *
   * @return array
   *   An array of settings currently set on the index.
   *
   * @throws \Drupal\search_api_meilisearch\Api\MeilisearchApiException
   *   Exception on an API call failure.
   *
   * @see https://www.meilisearch.com/docs/reference/api/settings#get-settings
   */
  public function getSettings(string $indexName): array;

  /**
   * Waits for the update to be finished.
   *
   * @param int $updateId
   *   The update id of the executed operation.
   *
   * @return array
   *   Returns task.
   *
   * @throws \Drupal\search_api_meilisearch\Api\MeilisearchApiException
   *   Exception on an API call failure.
   *
   * @see https://www.meilisearch.com/docs/learn/async/asynchronous_operations
   */
  public function waitForUpdate(int $updateId): array;

  /**
   * Gets the synonyms saved on the server.
   *
   * @param string $indexName
   *   The name of the index.
   *
   * @return array
   *   An array of synonyms.
   *
   * @throws \Drupal\search_api_meilisearch\Api\MeilisearchApiException
   *   Exception on an API call failure.
   *
   * @see https://www.meilisearch.com/docs/reference/api/settings#get-synonyms
   */
  public function getSynonyms(string $indexName): array;

  /**
   * Update synonyms saved on the Meilisearch server.
   *
   * @param string $indexName
   *   The name of the index.
   * @param array $synonyms
   *   An array of synonyms.
   *
   * @return array
   *   An update id.
   *
   * @throws \Drupal\search_api_meilisearch\Api\MeilisearchApiException
   *   Exception on an API call failure.
   *
   * @see https://www.meilisearch.com/docs/reference/api/settings#update-synonyms
   */
  public function updateSynonyms(string $indexName, array $synonyms): array;

  /**
   * Clear all synonyms stored for the specific index.
   *
   * @param string $indexName
   *   The name of the index.
   *
   * @return array
   *   An update id.
   *
   * @throws \Drupal\search_api_meilisearch\Api\MeilisearchApiException
   *   Exception on an API call failure.
   *
   * @see https://www.meilisearch.com/docs/reference/api/settings#reset-synonyms
   */
  public function resetSynonyms(string $indexName): array;

  /**
   * Updates the stop words that are stored on the Meilisearch server.
   *
   * @param string $indexName
   *   The name of the index.
   * @param array $stopwords
   *   A list of stop words.
   *
   * @return array
   *   An update id.
   *
   * @throws \Drupal\search_api_meilisearch\Api\MeilisearchApiException
   *   Exception on an API call failure.
   *
   * @see https://www.meilisearch.com/docs/reference/api/settings#update-stop-words
   */
  public function updateStopWords(string $indexName, array $stopwords): array;

  /**
   * Resets the stop words that are stored on the server.
   *
   * @param string $indexName
   *   The name of the index.
   *
   * @return array
   *   An update id.
   *
   * @throws \Drupal\search_api_meilisearch\Api\MeilisearchApiException
   *   Exception on an API call failure.
   *
   * @see https://www.meilisearch.com/docs/reference/api/settings#reset-stop-words
   */
  public function resetStopWords(string $indexName): array;

  /**
   * Gets ranking rules stored on the server.
   *
   * @param string $index_name
   *   The name of the index.
   *
   * @return array
   *   An array of ranking rules.
   *
   * @throws \Drupal\search_api_meilisearch\Api\MeilisearchApiException
   *   Exception on an API call failure.
   *
   * @see https://www.meilisearch.com/docs/reference/api/settings#get-ranking-rules
   */
  public function getRankingRules(string $index_name): array;

  /**
   * Updates the ranking rules stored on the server.
   *
   * @param string $index_name
   *   The name of the index.
   * @param array $rankingRules
   *   An array of ranking rules.
   *
   * @return array
   *   An update id.
   *
   * @throws \Drupal\search_api\SearchApiException
   *   Exception on an API call failure.
   *
   * @see https://www.meilisearch.com/docs/reference/api/settings#update-ranking-rules
   */
  public function setRankingRules(string $index_name, array $rankingRules): array;

  /**
   * Gets the list of attributes that can be used for sorting.
   *
   * @param string $index_name
   *   The name of the index.
   *
   * @return array
   *   An array of sortable attributes.
   *
   * @throws \Drupal\search_api\SearchApiException
   *   Exception on an API call failure.
   *
   * @see https://www.meilisearch.com/docs/reference/api/settings#get-sortable-attributes
   */
  public function getSortableAttributes(string $index_name): array;

  /**
   * Updates the list of attributes that can be used for sorting.
   *
   * @param string $index_name
   *   The name of the index.
   * @param array $sortableAttributes
   *   An array of sortable attributes.
   *
   * @return array
   *   An update id.
   *
   * @throws \Drupal\search_api\SearchApiException
   *   Exception on an API call failure.
   *
   * @see https://www.meilisearch.com/docs/reference/api/settings#update-sortable-attributes
   */
  public function setSortableAttributes(string $index_name, array $sortableAttributes): array;

  /**
   * Gets the list of attributes that can be used for searching.
   *
   * @param string $indexName
   *   The name of the index.
   *
   * @return string[]
   *   An array of searchable attributes.
   *
   * @throws \Drupal\search_api\SearchApiException
   *   Exception on an API call failure.
   *
   * @see https://www.meilisearch.com/docs/reference/api/settings#get-searchable-attributes
   */
  public function getSearchableAttributes(string $indexName): array;

  /**
   * Updates the list of attributes that can be used for searching.
   *
   * @param string $indexName
   *   The name of the index.
   * @param string[] $searchableAttributes
   *   An array of sortable attributes.
   *
   * @return array
   *   The async task data.
   *
   * @throws \Drupal\search_api\SearchApiException
   *   Exception on an API call failure.
   *
   * @see https://www.meilisearch.com/docs/reference/api/settings#update-searchable-attributes
   */
  public function setSearchableAttributes(string $indexName, array $searchableAttributes): array;

  /**
   * Checks if the server is available.
   *
   * @return bool
   *   Returns TRUE if the server can be reached.
   *
   * @throws \Drupal\search_api_meilisearch\Api\MeilisearchApiException
   *    Exception on an API call failure.
   */
  public function ping(): bool;

  /**
   * Returns the private and public keys.
   *
   * @return \MeiliSearch\Contracts\KeysResults
   *   Private and public key or FALSE on failure.
   *
   * @throws \Drupal\search_api_meilisearch\Api\MeilisearchApiException
   *   Exception on an API call failure.
   *
   * @see https://www.meilisearch.com/docs/reference/api/keys#get-all-keys
   */
  public function keys(): KeysResults;

  /**
   * Searches for facets.
   *
   * @param string $indexId
   *   The name of the index.
   * @param string $facetName
   *   The name of the facet.
   * @param string|null $facetQuery
   *   The facet query to search by.
   * @param array|null $filter
   *   The filter to search by.
   * @param string|null $query
   *   The query to search by.
   *
   * @return \Meilisearch\Search\FacetSearchResult
   *   The facets result.
   *
   * @throws \Drupal\search_api_meilisearch\Api\MeilisearchApiException
   * @throws \Drupal\Component\Plugin\Exception\InvalidPluginDefinitionException
   * @throws \Drupal\Component\Plugin\Exception\PluginNotFoundException
   */
  public function searchFacets(string $indexId, string $facetName, ?string $facetQuery = NULL, ?array $filter = NULL, ?string $query = NULL): FacetSearchResult;

}
