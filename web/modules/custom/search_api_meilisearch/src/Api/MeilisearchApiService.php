<?php

namespace Drupal\search_api_meilisearch\Api;

use <PERSON><PERSON><PERSON>\search_api_meilisearch\Client\MeilisearchClientFactoryInterface;
use Mei<PERSON>earch\Client;
use Meilisearch\Contracts\DocumentsQuery;
use <PERSON>lisear<PERSON>\Contracts\DocumentsResults;
use Mei<PERSON>earch\Contracts\FacetSearchQuery;
use Meilisearch\Contracts\IndexesResults;
use Meilisearch\Contracts\KeysResults;
use Meilisearch\Endpoints\Indexes;
use Meilisearch\Search\FacetSearchResult;

/**
 * Provides a service to interact with Meilisearch API.
 */
class MeilisearchApiService implements MeilisearchApiServiceInterface {

  /**
   * The master key of the server.
   *
   * @var string
   */
  protected string $masterKey = '';

  /**
   * The URL for accessing the meilisearch server.
   *
   * @var string
   */
  protected string $url = '';

  /**
   * The Meilisearch client.
   *
   * @var \MeiliSearch\Client|null
   */
  protected ?Client $client = NULL;

  /**
   * The Meilisearch Client factory.
   *
   * @var \Drupal\search_api_meilisearch\Client\MeilisearchClientFactoryInterface
   */
  protected MeilisearchClientFactoryInterface $clientFactory;

  /**
   * MeilisearchApiService constructor.
   *
   * @param \Drupal\search_api_meilisearch\Client\MeilisearchClientFactoryInterface $client_factory
   *   The Meilisearch client factory.
   */
  public function __construct(MeilisearchClientFactoryInterface $client_factory) {
    $this->clientFactory = $client_factory;
  }

  /**
   * {@inheritdoc}
   */
  public function getMasterKey(): string {
    return $this->masterKey;
  }

  /**
   * {@inheritdoc}
   */
  public function setMasterKey(string $key) {
    $this->masterKey = $key;
  }

  /**
   * {@inheritdoc}
   */
  public function getUrl(): string {
    return $this->url;
  }

  /**
   * {@inheritdoc}
   */
  public function setUrl(string $url) {
    $this->url = $url;
  }

  /**
   * {@inheritdoc}
   */
  public function connection(): Client {
    if ($this->client == NULL) {
      $this->client = $this->clientFactory->getInstance($this->getUrl(), $this->getMasterKey());
      try {
        $this->client->version();
      }
      catch (\Exception $e) {
        throw new MeilisearchApiException($e->getMessage(), $e->getCode(), $e);
      }
    }
    return $this->client;
  }

  /**
   * {@inheritdoc}
   */
  public function search(string $indexName, string $query, array $options = []) {
    try {
      $index = $this->getIndex($indexName);
      return $index->search($query, $options);
    }
    catch (\Exception $e) {
      throw new MeilisearchApiException($e->getMessage(), $e->getCode(), $e);
    }
  }

  /**
   * {@inheritdoc}
   */
  public function getIndex(string $indexName): Indexes {
    try {
      return $this->connection()->getIndex($indexName);
    }
    catch (\Exception $e) {
      throw new MeilisearchApiException($e->getMessage(), $e->getCode(), $e);
    }
  }

  /**
   * {@inheritdoc}
   */
  public function createIndex($attributes): array {
    try {
      return $this->connection()->createIndex($attributes);
    }
    catch (\Exception $e) {
      throw new MeilisearchApiException($e->getMessage(), $e->getCode(), $e);
    }
  }

  /**
   * {@inheritdoc}
   */
  public function removeIndex(string $indexName): bool {
    try {
      $task = $this->connection()->deleteIndex($indexName);
      $task = $this->connection()->waitForTask($task['taskUid']);
      return $task['status'] !== 'failed';
    }
    catch (\Exception $e) {
      throw new MeilisearchApiException($e->getMessage(), $e->getCode(), $e);
    }
  }

  /**
   * {@inheritdoc}
   */
  public function listIndexes(): IndexesResults {
    try {
      return $this->connection()->getIndexes();
    }
    catch (\Exception $e) {
      throw new MeilisearchApiException($e->getMessage(), $e->getCode(), $e);
    }
  }

  /**
   * {@inheritdoc}
   */
  public function addDocuments(string $indexName, array $documents = []): array {
    try {
      $index = $this->getIndex($indexName);
      return $index->addDocuments($documents, 'id');
    }
    catch (\Exception $e) {
      throw new MeilisearchApiException($e->getMessage(), $e->getCode(), $e);
    }
  }

  /**
   * {@inheritdoc}
   */
  public function getDocument(string $indexName, string $id): array {
    try {
      $index = $this->getIndex($indexName);
      return $index->getDocument($id);
    }
    catch (\Exception $e) {
      throw new MeilisearchApiException($e->getMessage(), $e->getCode(), $e);
    }
  }

  /**
   * {@inheritdoc}
   */
  public function getDocuments(string $indexName, ?DocumentsQuery $options = NULL): DocumentsResults {
    try {
      $index = $this->getIndex($indexName);
      return $index->getDocuments($options);
    }
    catch (\Exception $e) {
      throw new MeilisearchApiException($e->getMessage(), $e->getCode(), $e);
    }
  }

  /**
   * {@inheritdoc}
   */
  public function deleteDocument(string $indexName, string $id): array {
    try {
      $index = $this->getIndex($indexName);
      return $index->deleteDocument($id);
    }
    catch (\Exception $e) {
      throw new MeilisearchApiException($e->getMessage(), $e->getCode(), $e);
    }
  }

  /**
   * {@inheritdoc}
   */
  public function deleteDocuments(string $indexName, array $ids): array {
    try {
      $index = $this->getIndex($indexName);
      return $index->deleteDocuments($ids);
    }
    catch (\Exception $e) {
      throw new MeilisearchApiException($e->getMessage(), $e->getCode(), $e);
    }
  }

  /**
   * {@inheritdoc}
   */
  public function deleteAllDocuments(string $indexName): array {
    try {
      return $this->getIndex($indexName)->deleteAllDocuments();
    }
    catch (\Exception $e) {
      throw new MeilisearchApiException($e->getMessage(), $e->getCode(), $e);
    }
  }

  /**
   * {@inheritdoc}
   */
  public function setFilterableAttributes(string $indexName, array $fields): array {
    try {
      return $this->getIndex($indexName)->updateFilterableAttributes($fields);
    }
    catch (\Exception $e) {
      throw new MeilisearchApiException($e->getMessage(), $e->getCode(), $e);
    }
  }

  /**
   * {@inheritdoc}
   */
  public function getFilterableAttributes(string $indexName): array {
    try {
      return $this->getIndex($indexName)->getFilterableAttributes();
    }
    catch (\Exception $e) {
      throw new MeilisearchApiException($e->getMessage(), $e->getCode(), $e);
    }
  }

  /**
   * {@inheritdoc}
   */
  public function setSettings(string $indexName, array $settings): array {
    try {
      return $this->getIndex($indexName)->updateSettings($settings);
    }
    catch (\Exception $e) {
      throw new MeilisearchApiException($e->getMessage(), $e->getCode(), $e);
    }
  }

  /**
   * {@inheritdoc}
   */
  public function getSettings(string $indexName): array {
    try {
      return $this->getIndex($indexName)->getSettings();
    }
    catch (\Exception $e) {
      throw new MeilisearchApiException($e->getMessage(), $e->getCode(), $e);
    }
  }

  /**
   * {@inheritdoc}
   */
  public function waitForUpdate(int $updateId): array {
    try {
      return $this->connection()->waitForTask($updateId);
    }
    catch (\Exception $e) {
      throw new MeilisearchApiException($e->getMessage(), $e->getCode(), $e);
    }
  }

  /**
   * {@inheritdoc}
   */
  public function getSynonyms(string $indexName): array {
    try {
      $index = $this->getIndex($indexName);
      return $index->getSynonyms();
    }
    catch (\Exception $e) {
      throw new MeilisearchApiException($e->getMessage(), $e->getCode(), $e);
    }
  }

  /**
   * {@inheritdoc}
   */
  public function updateSynonyms(string $indexName, array $synonyms): array {
    try {
      $index = $this->getIndex($indexName);
      $this->resetSynonyms($indexName);
      return $index->updateSynonyms($synonyms);
    }
    catch (\Exception $e) {
      throw new MeilisearchApiException($e->getMessage(), $e->getCode(), $e);
    }
  }

  /**
   * {@inheritdoc}
   */
  public function resetSynonyms(string $indexName): array {
    try {
      $index = $this->getIndex($indexName);
      return $index->resetSynonyms();
    }
    catch (\Exception $e) {
      throw new MeilisearchApiException($e->getMessage(), $e->getCode(), $e);
    }
  }

  /**
   * {@inheritdoc}
   */
  public function updateStopWords(string $indexName, array $stopwords): array {
    try {
      $index = $this->getIndex($indexName);
      return $index->updateStopWords($stopwords);
    }
    catch (\Exception $e) {
      throw new MeilisearchApiException($e->getMessage(), $e->getCode(), $e);
    }
  }

  /**
   * {@inheritdoc}
   */
  public function resetStopWords(string $indexName): array {
    try {
      $index = $this->getIndex($indexName);
      return $index->resetStopWords();
    }
    catch (\Exception $e) {
      throw new MeilisearchApiException($e->getMessage(), $e->getCode(), $e);
    }
  }

  /**
   * {@inheritdoc}
   */
  public function getRankingRules(string $index_name): array {
    try {
      $index = $this->getIndex($index_name);
      return $index->getRankingRules();
    }
    catch (\Exception $e) {
      throw new MeilisearchApiException($e->getMessage(), $e->getCode(), $e);
    }
  }

  /**
   * {@inheritdoc}
   */
  public function setRankingRules(string $index_name, array $rankingRules): array {
    try {
      $index = $this->getIndex($index_name);
      return $index->updateRankingRules($rankingRules);
    }
    catch (\Exception $e) {
      throw new MeilisearchApiException($e->getMessage(), $e->getCode(), $e);
    }
  }

  /**
   * {@inheritdoc}
   */
  public function getSortableAttributes(string $index_name): array {
    try {
      $index = $this->getIndex($index_name);
      return $index->getSortableAttributes();
    }
    catch (\Exception $e) {
      throw new MeilisearchApiException($e->getMessage(), $e->getCode(), $e);
    }
  }

  /**
   * {@inheritdoc}
   */
  public function setSortableAttributes(string $index_name, array $sortableAttributes): array {
    try {
      $index = $this->getIndex($index_name);
      return $index->updateSortableAttributes($sortableAttributes);
    }
    catch (\Exception $e) {
      throw new MeilisearchApiException($e->getMessage(), $e->getCode(), $e);
    }
  }

  /**
   * {@inheritdoc}
   */
  public function getSearchableAttributes(string $indexName): array {
    try {
      return $this->getIndex($indexName)->getSearchableAttributes();
    }
    catch (\Exception $e) {
      throw new MeilisearchApiException($e->getMessage(), $e->getCode(), $e);
    }
  }

  /**
   * {@inheritdoc}
   */
  public function setSearchableAttributes(string $indexName, array $searchableAttributes): array {
    try {
      return $this->getIndex($indexName)->updateSearchableAttributes($searchableAttributes);
    }
    catch (\Exception $e) {
      throw new MeilisearchApiException($e->getMessage(), $e->getCode(), $e);
    }
  }

  /**
   * {@inheritdoc}
   */
  public function searchFacets(string $indexId, string $facetName, ?string $facetQuery = NULL, ?array $filter = NULL, ?string $query = NULL): FacetSearchResult {
    try {
      $index = $this->getIndex($indexId);

      $facetSearch = new FacetSearchQuery();
      $facetSearch->setFacetName($facetName);

      if (!empty($facetQuery)) {
        $facetSearch->setFacetQuery($facetQuery);
      }
      if (!empty($filter)) {
        $facetSearch->setFilter($filter);
      }
      if (!empty($query)) {
        $facetSearch->setQuery($query);
      }

      return $index->facetSearch($facetSearch);
    }
    catch (MeilisearchApiException $e) {
      throw new MeilisearchApiException($e->getMessage(), $e->getCode(), $e);
    }
  }

  /**
   * {@inheritdoc}
   */
  public function ping(): bool {
    try {
      $this->connection();
      return TRUE;
    }
    catch (\Exception $e) {
      throw new MeilisearchApiException($e->getMessage(), $e->getCode(), $e);
    }
  }

  /**
   * {@inheritdoc}
   */
  public function keys(): KeysResults {
    try {
      return $this->connection()->getKeys();
    }
    catch (\Exception $e) {
      throw new MeilisearchApiException($e->getMessage(), $e->getCode(), $e);
    }
  }

}
