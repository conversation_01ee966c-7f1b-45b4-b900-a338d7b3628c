<?php

namespace Drupal\search_api_meilisearch\Parser;

use <PERSON><PERSON>al\search_api\IndexInterface;
use Dr<PERSON>al\search_api\Query\ConditionInterface;
use Drupal\search_api_meilisearch\Utility\MeilisearchUtils;

/**
 * Parses search api condition with IN operator.
 */
class InOperatorParser implements ConditionParserInterface {

  /**
   * {@inheritdoc}
   */
  public function parse(ConditionInterface $condition, IndexInterface $index): string {
    $value = $condition->getValue();
    $field = $condition->getField();
    $condition = array_reduce($value, function (string $prev, string $item) use ($field) {
      $item = MeilisearchUtils::formatConditionValue($item);
      return $prev . $field . ' = ' . $item . ' OR ';
    }, '');

    return '(' . rtrim($condition, ' OR') . ')';
  }

  /**
   * {@inheritdoc}
   */
  public function supports(ConditionInterface $condition, IndexInterface $index): bool {
    if ($condition->getOperator() !== 'IN') {
      return FALSE;
    }

    $value = $condition->getValue();

    return is_array($value) && count($value) > 0;
  }

}
