<?php

namespace Drupal\search_api_meilisearch\Parser;

use <PERSON><PERSON><PERSON>\search_api\IndexInterface;
use <PERSON><PERSON><PERSON>\search_api\Query\ConditionInterface;

/**
 * Defines an interface for condition parser plugins.
 */
interface ConditionParserInterface {

  /**
   * Checks if the plugin supports parsing.
   *
   * @param \Drupal\search_api\Query\ConditionInterface $condition
   *   The condition object to check.
   * @param \Drupal\search_api\IndexInterface $index
   *   The search api index the condition belongs to.
   *
   * @return bool
   *   TRUE if plugin supports parsing, false otherwise.
   */
  public function supports(ConditionInterface $condition, IndexInterface $index): bool;

  /**
   * Parses the condition into a string.
   *
   * @param \Drupal\search_api\Query\ConditionInterface $condition
   *   The condition object to parse.
   * @param \Drupal\search_api\IndexInterface $index
   *   The search api index the condition belongs to.
   *
   * @return string
   *   The string representation of a condition.
   */
  public function parse(ConditionInterface $condition, IndexInterface $index): string;

}
