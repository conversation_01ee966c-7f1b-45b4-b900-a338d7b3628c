<?php

namespace Drupal\search_api_meilisearch\Parser;

use <PERSON><PERSON>al\search_api\IndexInterface;
use <PERSON><PERSON>al\search_api\Query\ConditionInterface;

/**
 * Parses search api condition with BETWEEN operator.
 */
class BetweenOperatorParser implements ConditionParserInterface {

  /**
   * {@inheritdoc}
   */
  public function parse(ConditionInterface $condition, IndexInterface $index): string {
    $value = $condition->getValue();

    return $condition->getField() . ' ' . $value[0] . ' TO ' . $value[1];
  }

  /**
   * {@inheritdoc}
   */
  public function supports(ConditionInterface $condition, IndexInterface $index): bool {
    if ($condition->getOperator() !== 'BETWEEN') {
      return FALSE;
    }

    $value = $condition->getValue();
    if (!is_array($value) || count($value) !== 2) {
      return FALSE;
    }

    if (!is_numeric($value[0]) || !is_numeric($value[1])) {
      return FALSE;
    }

    return TRUE;
  }

}
