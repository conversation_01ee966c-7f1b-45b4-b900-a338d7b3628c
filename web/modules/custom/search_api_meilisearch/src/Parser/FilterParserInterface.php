<?php

namespace Dr<PERSON>al\search_api_meilisearch\Parser;

use <PERSON><PERSON><PERSON>\search_api\IndexInterface;
use <PERSON><PERSON><PERSON>\search_api\Query\ConditionGroupInterface;

/**
 * Provides an interface for parsing conditions group to Meilisearch filter.
 */
interface FilterParserInterface {

  /**
   * Converts the conditions group to the Meilisearch string expression filter.
   *
   * @param \Drupal\search_api\Query\ConditionGroupInterface $conditionGroup
   *   The condition group.
   * @param \Drupal\search_api\IndexInterface $index
   *   The index the condition group belongs to.
   *
   * @return string
   *   The string expression filter.
   */
  public function parseExpression(ConditionGroupInterface $conditionGroup, IndexInterface $index): string;

}
