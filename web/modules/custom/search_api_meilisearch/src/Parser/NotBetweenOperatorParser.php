<?php

namespace Drupal\search_api_meilisearch\Parser;

use <PERSON><PERSON>al\search_api\IndexInterface;
use Dr<PERSON>al\search_api\Query\ConditionInterface;

/**
 * Parses search api conditions with NOT BETWEEN operator.
 */
class NotBetweenOperatorParser implements ConditionParserInterface {

  /**
   * {@inheritdoc}
   */
  public function parse(ConditionInterface $condition, IndexInterface $index): string {
    $value = $condition->getValue();
    $field = $condition->getField();

    return '(' . $field . ' < ' . $value[0] . ' OR ' . $field . ' > ' . $value[1] . ')';
  }

  /**
   * {@inheritdoc}
   */
  public function supports(ConditionInterface $condition, IndexInterface $index): bool {
    if ($condition->getOperator() !== 'NOT BETWEEN') {
      return FALSE;
    }

    $value = $condition->getValue();
    if (!is_array($value) || count($value) !== 2) {
      return FALSE;
    }

    if (!is_numeric($value[0]) || !is_numeric($value[1])) {
      return FALSE;
    }

    return TRUE;
  }

}
