<?php

namespace Drupal\search_api_meilisearch\Parser;

use <PERSON><PERSON>al\search_api\IndexInterface;
use <PERSON><PERSON>al\search_api\Query\ConditionInterface;
use Drupal\search_api_meilisearch\Utility\MeilisearchUtils;

/**
 * Parses search api conditions with NOT IN operator.
 */
class NotInOperatorParser implements ConditionParserInterface {

  /**
   * {@inheritdoc}
   */
  public function parse(ConditionInterface $condition, IndexInterface $index): string {
    $value = $condition->getValue();
    $field = $condition->getField();
    $condition = array_reduce($value, function (string $prev, string $item) use ($field) {
      $item = MeilisearchUtils::formatConditionValue($item);
      return $prev . $field . ' != ' . $item . ' AND ';
    }, '');

    return '(' . rtrim($condition, ' AND') . ')';
  }

  /**
   * {@inheritdoc}
   */
  public function supports(ConditionInterface $condition, IndexInterface $index): bool {
    if ($condition->getOperator() !== 'NOT IN') {
      return FALSE;
    }

    $value = $condition->getValue();
    if (!is_array($value) || !count($value)) {
      return FALSE;
    }

    return TRUE;
  }

}
