<?php

namespace Drupal\search_api_meilisearch\Parser;

use <PERSON><PERSON>al\search_api\IndexInterface;
use <PERSON><PERSON>al\search_api\Query\ConditionInterface;

/**
 * Parses search api conditions with null value.
 */
class NullValueParser implements ConditionParserInterface {

  /**
   * {@inheritdoc}
   */
  public function parse(ConditionInterface $condition, IndexInterface $index): string {
    $operator = $condition->getOperator();
    if ($operator === '<>') {
      return $condition->getField() . ' EXISTS';
    }

    return $condition->getField() . ' NOT EXISTS';
  }

  /**
   * {@inheritdoc}
   */
  public function supports(ConditionInterface $condition, IndexInterface $index): bool {
    $operator = $condition->getOperator();
    if ($operator !== '=' && $operator !== '<>') {
      return FALSE;
    }

    return is_null($condition->getValue());
  }

}
