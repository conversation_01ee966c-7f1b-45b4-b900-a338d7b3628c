<?php

namespace Drupal\search_api_meilisearch\Parser;

use <PERSON><PERSON><PERSON>\search_api\IndexInterface;
use <PERSON><PERSON>al\search_api\Query\ConditionInterface;
use Drupal\search_api_meilisearch\Utility\MeilisearchUtils;

/**
 * Parses search api conditions with scalar value.
 */
class ScalarValueParser implements ConditionParserInterface {

  protected const INVALID_OPERATORS = [
    'IN',
    'NOT IN',
    'BETWEEN',
    'NOT BETWEEN',
  ];

  /**
   * {@inheritdoc}
   */
  public function parse(ConditionInterface $condition, IndexInterface $index): string {
    $value = MeilisearchUtils::formatConditionValue($condition->getValue());
    $operator = $condition->getOperator();
    if ($operator === '<>') {
      $operator = '!=';
    }

    return $condition->getField() . ' ' . $operator . ' ' . $value;
  }

  /**
   * {@inheritdoc}
   */
  public function supports(ConditionInterface $condition, IndexInterface $index): bool {
    if (in_array($condition->getOperator(), self::INVALID_OPERATORS)) {
      return FALSE;
    }

    return is_scalar($condition->getValue());
  }

}
