<?php

namespace Dr<PERSON>al\search_api_meilisearch\Plugin\search_api\processor;

use <PERSON><PERSON><PERSON>\search_api\IndexInterface;
use <PERSON><PERSON><PERSON>\search_api\Processor\ProcessorPluginBase;
use <PERSON><PERSON><PERSON>\search_api\Query\QueryInterface;
use <PERSON><PERSON>al\search_api_meilisearch\Plugin\search_api\backend\SearchApiMeilisearchBackend;

/**
 * Adds language filter to the Meilisearch query.
 *
 * @SearchApiProcessor(
 *   id = "search_api_meilisearch_language_filter",
 *   label = @Translation("Language filter"),
 *   description = @Translation("Adds language filter to the Meilisearch query."),
 *   stages = {
 *     "preprocess_query" = 0,
 *   },
 *   locked = true,
 *   hidden = true,
 * )
 */
class MeilisearchLanguageFilter extends ProcessorPluginBase {

  /**
   * {@inheritdoc}
   */
  public function preprocessSearchQuery(QueryInterface $query): void {
    // Check if the backend is Meilisearch.
    // Can't use supportsIndex() because that is only for UI visibility.
    if (!self::hasMeilisearchBackend($query->getIndex())) {
      return;
    }

    $languages = $query->getLanguages();
    if (!$languages) {
      return;
    }

    $conditionGroup = $query->getConditionGroup();
    $conditionGroup->addCondition('search_api_language', $languages, 'IN');
  }

  /**
   * {@inheritdoc}
   */
  public static function supportsIndex(IndexInterface $index): bool {
    return self::hasMeilisearchBackend($index);
  }

  /**
   * Checks if an index is attached to the Meilisearch backend plugin.
   *
   * @param \Drupal\search_api\IndexInterface $index
   *   The index to check.
   *
   * @return bool
   *   TRUE if index is attached to Meilisearch, FALSE otherwise.
   *
   * @throws \Drupal\search_api\SearchApiException
   */
  protected static function hasMeilisearchBackend(IndexInterface $index): bool {
    if (!$index->getServerInstance()) {
      return FALSE;
    }

    return $index->getServerInstance()->getBackend() instanceof SearchApiMeilisearchBackend;
  }

}
