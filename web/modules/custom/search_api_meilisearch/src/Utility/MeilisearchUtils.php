<?php

namespace Drupal\search_api_meilisearch\Utility;

/**
 * Provides static utility methods.
 */
class MeilisearchUtils {

  /**
   * Formats a string to the meilisearch document primary key requirements.
   *
   * @param string $id
   *   The id to format.
   *
   * @return string
   *   The formatted id.
   *
   * @see https://www.meilisearch.com/docs/learn/core_concepts/primary_key#formatting-the-document-id
   */
  public static function formatAsDocumentId(string $id): string {
    return preg_replace('/[^A-Za-z0-9_-]/', '_', $id);
  }

  /**
   * Formats the filter condition value per Meilisearch filter specifications.
   *
   * The method escapes any double quotes inside the value then wraps the value
   * with double quotes. Used for wrapping values in filter condition
   * expressions so the Meilisearch parses them properly.
   *
   * @param string $value
   *   The value to quote.
   *
   * @return string
   *   The quoted value.
   *
   * @see https://www.meilisearch.com/docs/learn/fine_tuning_results/filtering#conditions
   */
  public static function formatConditionValue(string $value): string {
    if (!is_numeric($value)) {
      $value = str_replace('\\', '\\\\', $value);
      return '"' . str_replace('"', '\"', $value) . '"';
    }

    return $value;
  }

}
