<?php

namespace Drupal\search_api_meilisearch\Converter\Field;

use <PERSON><PERSON><PERSON>\search_api\Item\FieldInterface;

/**
 * Provides an interface for converting search api fields into different values.
 */
interface FieldConverterInterface {

  /**
   * Checks if a field is applicable for conversion.
   *
   * @param \Drupal\search_api\Item\FieldInterface $field
   *   The search api field.
   *
   * @return bool
   *   TRUE if field can be converted, FALSE otherwise.
   */
  public function supports(FieldInterface $field): bool;

  /**
   * Converts the field into an list of values.
   *
   * @param \Drupal\search_api\Item\FieldInterface $field
   *   The search api field.
   *
   * @return array
   *   The list of values.
   */
  public function toValues(FieldInterface $field): array;

}
