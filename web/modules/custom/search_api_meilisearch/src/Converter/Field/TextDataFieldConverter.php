<?php

namespace Drupal\search_api_meilisearch\Converter\Field;

use <PERSON><PERSON>al\search_api\Item\FieldInterface;
use <PERSON><PERSON>al\search_api\Plugin\search_api\data_type\TextDataType;

/**
 * Converts the search api text data field values to string values.
 *
 * @see \Drupal\search_api\Plugin\search_api\data_type\TextDataType
 */
class TextDataFieldConverter implements FieldConverterInterface {

  /**
   * {@inheritdoc}
   */
  public function toValues(FieldInterface $field): array {
    return array_map(function ($value) {
      return (string) $value;
    }, $field->getValues());
  }

  /**
   * {@inheritdoc}
   */
  public function supports(FieldInterface $field): bool {
    return $field->getDataTypePlugin() instanceof TextDataType;
  }

}
