<?php

namespace Drupal\search_api_meilisearch\Converter\Item;

/**
 * Provides an interface for converting search api items into an array.
 */
interface ItemConverterInterface {

  /**
   * Converts a list of search api items to an array of meilisearch documents.
   *
   * @param \Drupal\search_api\Item\ItemInterface[] $items
   *   The list of search api items.
   *
   * @return array
   *   A list of meilisearch documents.
   */
  public function convertToDocuments(array $items): array;

}
