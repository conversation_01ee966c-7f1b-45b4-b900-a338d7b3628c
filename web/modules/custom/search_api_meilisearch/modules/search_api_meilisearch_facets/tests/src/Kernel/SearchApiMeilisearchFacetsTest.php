<?php

namespace Drupal\Tests\search_api_meilisearch_facets\Kernel;

use <PERSON><PERSON>al\entity_test\Entity\EntityTest;
use <PERSON><PERSON>al\search_api\Entity\Index;
use Drupal\search_api\Entity\Server;
use Drupal\search_api\Query\Query;
use Drupal\Tests\search_api_meilisearch\Kernel\Backend\BackendKernelTestBase;

/**
 * Tests the Search API MeiliSearch facets module.
 *
 * @requires module facets
 * @group search_api_meilisearch
 */
class SearchApiMeilisearchFacetsTest extends BackendKernelTestBase {

  protected const TEST_INDEX = 'kernel_facets_test_index';

  /**
   * {@inheritdoc}
   */
  protected static $modules = [
    'user',
    'system',
    'entity_test',
    'search_api',
    'search_api_meilisearch',
    'search_api_meilisearch_facets',
  ];

  /**
   * Tests facets on the search api index.
   *
   * @throws \Drupal\search_api\SearchApiException
   * @throws \Drupal\search_api_meilisearch\Api\MeilisearchApiException
   * @throws \Drupal\Core\Entity\EntityStorageException
   */
  public function testFacets(): void {
    $this->prepareTestIndex(self::TEST_INDEX);
    $this->populateIndex();

    // Test without filters.
    $query = $this->prepareQueryWithFacets();
    $results = $query->execute();

    $this->assertCount(3, $results->getExtraData('search_api_facets')['name']);
    $this->assertCount(3, $results->getExtraData('search_api_facets')['weight']);

    // Test with filters.
    $query = $this->prepareQueryWithFacets();
    $query->addCondition('name', 'Test entity 1');
    $results = $query->execute();

    // First test that the results will have all facets present,
    // even if they are not present in the results.
    $this->assertCount(3, $results->getExtraData('search_api_facets')['name']);
    $this->assertCount(3, $results->getExtraData('search_api_facets')['weight']);

    // Test that the results have the correct count for the facets.
    foreach ($results->getExtraData('search_api_facets')['name'] as $facet) {
      if ($facet['filter'] === 'Test entity 1') {
        $this->assertEquals(1, $facet['count']);
      }
      else {
        $this->assertEquals(0, $facet['count']);
      }
    }

    // Test with empty result.
    $query = $this->prepareQueryWithFacets();
    $query->addCondition('name', 'Test entity 100');
    $results = $query->execute();

    foreach ($results->getExtraData('search_api_facets')['name'] as $facet) {
      $this->assertEquals(0, $facet['count']);
    }
  }

  /**
   * {@inheritdoc}
   *
   * @throws \Drupal\Core\Entity\EntityStorageException
   */
  protected function prepareTestIndex(string $indexName, bool $readOnly = FALSE): void {
    $server = Server::create([
      'id' => 'test_server',
      'name' => 'Test server',
      'backend' => 'search_api_meilisearch',
      'backend_config' => [
        'meilisearch_host_address' => getenv('MEILI_TEST_HOST') ?: 'http://meilisearch',
        'meilisearch_host_port' => getenv('MEILI_TEST_PORT') ?: '7700',
        'meilisearch_master_key' => getenv('MEILI_TEST_MASTER_KEY') ?: 'MASTER_KEY',
      ],
    ]);
    $server->save();

    $this->index = Index::create([
      'id' => $indexName,
      'name' => 'Test index',
      'datasource_settings' => [
        'entity:entity_test' => [],
      ],
      'field_settings' => [
        'name' => [
          'type' => 'string',
          'datasource_id' => 'entity:entity_test',
          'property_path' => 'name',
        ],
        'weight' => [
          'type' => 'string',
          'datasource_id' => 'entity:entity_test',
          'property_path' => 'weight',
        ],
        'body' => [
          'type' => 'string',
          'datasource_id' => 'entity:entity_test',
          'property_path' => 'body',
        ],
      ],
      'tracker_settings' => [
        'default' => [],
      ],
      'server' => $server->id(),
      'read_only' => $readOnly,
    ]);
    $this->index->save();
  }

  /**
   * Populates the index with entities.
   *
   * @throws \Drupal\Core\Entity\EntityStorageException
   */
  protected function populateIndex(): void {
    foreach ([10, 20, 30] as $key => $weight) {
      EntityTest::create([
        'name' => 'Test entity ' . $key,
        'body' => 'This is the body of entity ' . $key,
        'weight' => $weight,
      ])->save();
    }

    $this->index->indexItems();
  }

  /**
   * Prepare the query instance with facets.
   *
   * @return \Drupal\search_api\Query\Query
   *   The query instance.
   *
   * @throws \Drupal\search_api\SearchApiException
   */
  protected function prepareQueryWithFacets(): Query {
    $query = $this->index->query()->range();
    // Add search_api_facets option.
    $query->setOption('search_api_facets', [
      'name' => [
        'field' => 'name',
        'limit' => 10,
      ],
      'weight' => [
        'field' => 'weight',
        'limit' => 10,
      ],
    ]);

    return $query;
  }

  /**
   * {@inheritdoc}
   */
  protected function getTestIndexes(): array {
    return [self::TEST_INDEX];
  }

}
