services:
  search_api_meilisearch_facets.determining_server_features_subscriber:
    class: <PERSON><PERSON><PERSON>\search_api_meilisearch_facets\EventSubscriber\DeterminingServerFeaturesSubscriber
    arguments: []
    tags:
      - { name: event_subscriber }

  search_api_meilisearch_facets.processing_results_subscriber:
    class: <PERSON><PERSON>al\search_api_meilisearch_facets\EventSubscriber\ProcessingResultsSubscriber
    arguments: ['@search_api_meilisearch.api', '@search_api_meilisearch.filter_parser']
    tags:
      - { name: event_subscriber }
