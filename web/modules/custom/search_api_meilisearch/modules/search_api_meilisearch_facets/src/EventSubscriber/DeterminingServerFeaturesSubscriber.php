<?php

namespace Drupal\search_api_meilisearch_facets\EventSubscriber;

use <PERSON><PERSON><PERSON>\search_api\Event\DeterminingServerFeaturesEvent;
use <PERSON><PERSON>al\search_api\Event\SearchApiEvents;
use <PERSON><PERSON>al\search_api_meilisearch\Plugin\search_api\backend\SearchApiMeilisearchBackend;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

/**
 * Subscribes to determining server features events.
 */
class DeterminingServerFeaturesSubscriber implements EventSubscriberInterface {

  /**
   * Adds search_api_facets to the list of supported features.
   *
   * @param \Drupal\search_api\Event\DeterminingServerFeaturesEvent $event
   *   The event.
   *
   * @throws \Drupal\search_api\SearchApiException
   */
  public function onDeterminingServerFeatures(DeterminingServerFeaturesEvent $event): void {
    $backend = $event->getServer()->getBackend();
    if ($backend instanceof SearchApiMeilisearchBackend) {
      $features = &$event->getFeatures();
      $features[] = 'search_api_facets';
      $features[] = 'search_api_facets_operator_or';
    }
  }

  /**
   * {@inheritdoc}
   */
  public static function getSubscribedEvents(): array {
    return [
      SearchApiEvents::DETERMINING_SERVER_FEATURES => 'onDeterminingServerFeatures',
    ];
  }

}
