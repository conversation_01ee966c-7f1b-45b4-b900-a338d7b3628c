<?php

namespace Drupal\search_api_meilisearch_autocomplete\EventSubscriber;

use <PERSON><PERSON><PERSON>\search_api\Event\DeterminingServerFeaturesEvent;
use Dr<PERSON>al\search_api\Event\SearchApiEvents;
use Dr<PERSON>al\search_api_meilisearch\Plugin\search_api\backend\SearchApiMeilisearchBackend;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

/**
 * Adds autocomplete functionality to the list of supported features.
 */
class DeterminingServerFeaturesSubscriber implements EventSubscriberInterface {

  /**
   * Adds autocomplete functionality to the list of supported features.
   *
   * @param \Drupal\search_api\Event\DeterminingServerFeaturesEvent $event
   *   The server features alter event.
   *
   * @throws \Drupal\search_api\SearchApiException
   */
  public function onDetermine(DeterminingServerFeaturesEvent $event): void {
    $backend = $event->getServer()->getBackend();
    if ($backend instanceof SearchApiMeilisearchBackend) {
      $features = &$event->getFeatures();
      $features[] = 'search_api_autocomplete';
    }
  }

  /**
   * {@inheritdoc}
   */
  public static function getSubscribedEvents(): array {
    return [
      SearchApiEvents::DETERMINING_SERVER_FEATURES => 'onDetermine',
    ];
  }

}
