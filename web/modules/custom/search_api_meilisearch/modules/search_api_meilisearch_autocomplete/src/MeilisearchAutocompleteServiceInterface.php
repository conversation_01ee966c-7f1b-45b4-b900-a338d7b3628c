<?php

namespace Drupal\search_api_meilisearch_autocomplete;

use <PERSON><PERSON><PERSON>\search_api\Query\QueryInterface;
use <PERSON><PERSON><PERSON>\search_api_autocomplete\SearchInterface;

/**
 * Interface for providing autocomplete functionality for meili searches.
 */
interface MeilisearchAutocompleteServiceInterface {

  /**
   * Autocomplete suggestions for user input using Suggester component.
   *
   * @param \Drupal\search_api\Query\QueryInterface $query
   *   A query representing the base search.
   * @param \Drupal\search_api_autocomplete\SearchInterface $search
   *   An object containing details about the search the user is on.
   * @param string $incompleteKey
   *   The start of a fulltext keyword for the search.
   * @param string $userInput
   *   The user input for the fulltext search keywords.
   *
   * @return \Drupal\search_api_autocomplete\Suggestion\SuggestionInterface[]
   *   An array of complete suggestions.
   */
  public function getSuggestions(QueryInterface $query, SearchInterface $search, string $incompleteKey, string $userInput): array;

}
