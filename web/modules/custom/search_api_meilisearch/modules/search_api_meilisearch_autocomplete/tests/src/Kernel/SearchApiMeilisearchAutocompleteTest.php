<?php

namespace Drupal\Tests\search_api_meilisearch_autocomplete\Kernel;

use <PERSON><PERSON><PERSON>\entity_test\Entity\EntityTest;
use <PERSON><PERSON><PERSON>\search_api\Entity\Index;
use <PERSON><PERSON><PERSON>\search_api\Entity\Server;
use <PERSON><PERSON>al\search_api_autocomplete\Entity\Search;
use Drupal\Tests\search_api_meilisearch\Kernel\Backend\BackendKernelTestBase;

/**
 * Provides test for autocomplete search.
 *
 * @requires module search_api_autocomplete
 * @group search_api_meilisearch
 */
class SearchApiMeilisearchAutocompleteTest extends BackendKernelTestBase {

  protected const TEST_INDEX = 'kernel_autocomplete_search_test_index';

  /**
   * {@inheritdoc}
   */
  protected static $modules = [
    'user',
    'system',
    'entity_test',
    'search_api',
    'search_api_meilisearch',
    'search_api_autocomplete',
    'search_api_meilisearch_autocomplete',
  ];

  /**
   * Tests that autocomplete search returns correct results.
   */
  public function testAutocomplete(): void {
    $this->prepareTestIndex(self::TEST_INDEX);
    $this->populateIndex();
    $query = $this->index->query()->range();
    $autocompleteSearch = new Search(['index_id' => self::TEST_INDEX], 'search_api_autocomplete_search');

    /** @var \Drupal\search_api_meilisearch\Plugin\search_api\backend\SearchApiMeilisearchBackend $backend */
    $backend = $query->getIndex()->getServerInstance()->getBackend();

    // Only get 3 results back or in this test case all.
    $query->setOption('limit', 3);
    $suggestions = $backend->getAutocompleteSuggestions($query, $autocompleteSearch, 'tes', 'tes');
    $this->assertEquals(3, count($suggestions));
    $this->assertEquals('Tes', $suggestions[0]->getUserInput());
    $this->assertEquals('t article 0', $suggestions[0]->getSuggestionSuffix());
    $this->assertEquals('Test article 0', $suggestions[0]->getSuggestedKeys());
    $this->assertNull($suggestions[0]->getResultsCount());

    $this->assertEquals('Tes', $suggestions[0]->getUserInput());
    $this->assertEquals('t article 2', $suggestions[2]->getSuggestionSuffix());
    $this->assertEquals('Test article 2', $suggestions[2]->getSuggestedKeys());

    // Only get 2 results back.
    $query->setOption('limit', 2);
    $newSuggestions = $backend->getAutocompleteSuggestions($query, $autocompleteSearch, 'art', 'art');
    $this->assertEquals(2, count($newSuggestions));
    $this->assertEquals('art', $newSuggestions[0]->getUserInput());
    $this->assertEquals('Test ', $newSuggestions[0]->getSuggestionPrefix());
    $this->assertEquals('icle 0', $newSuggestions[0]->getSuggestionSuffix());
    $this->assertEquals('Test article 0', $newSuggestions[0]->getSuggestedKeys());

    $this->assertEquals('art', $newSuggestions[1]->getUserInput());
    $this->assertEquals('Test ', $newSuggestions[1]->getSuggestionPrefix());
    $this->assertEquals('icle 1', $newSuggestions[1]->getSuggestionSuffix());
    $this->assertEquals('Test article 1', $newSuggestions[1]->getSuggestedKeys());
  }

  /**
   * Prepares search api index with items for tests.
   */
  protected function prepareTestIndex(string $indexName, bool $readOnly = FALSE): void {
    $server = Server::create([
      'id' => 'test_server',
      'name' => 'Test server',
      'backend' => 'search_api_meilisearch',
      'backend_config' => [
        'meilisearch_host_address' => getenv('MEILI_TEST_HOST') ?: 'http://meilisearch',
        'meilisearch_host_port' => getenv('MEILI_TEST_PORT') ?: '7700',
        'meilisearch_master_key' => getenv('MEILI_TEST_MASTER_KEY') ?: 'MASTER_KEY',
      ],
    ]);
    $server->save();

    $this->index = Index::create([
      'id' => $indexName,
      'name' => 'Test index',
      'datasource_settings' => [
        'entity:entity_test' => [],
      ],
      'field_settings' => [
        'name' => [
          'type' => 'text',
          'datasource_id' => 'entity:entity_test',
          'property_path' => 'name',
          'label' => 'Name',
        ],
        'weight' => [
          'type' => 'integer',
          'datasource_id' => 'entity:entity_test',
          'property_path' => 'weight',
          'label' => 'Weight',
        ],
        'body' => [
          'type' => 'string',
          'datasource_id' => 'entity:entity_test',
          'property_path' => 'body',
          'label' => 'Body',
        ],
      ],
      'tracker_settings' => [
        'default' => [],
      ],
      'server' => $server->id(),
      'read_only' => $readOnly,
    ]);
    $this->index->save();
  }

  /**
   * Populates the index with entities.
   */
  protected function populateIndex(): void {
    foreach ([11, 22, 33] as $key => $weight) {
      EntityTest::create([
        'name' => 'Test article ' . $key,
        'body' => 'This is the body of test article entity' . $key,
        'weight' => $weight,
      ])->save();
    }

    $this->index->indexItems();
  }

  /**
   * {@inheritdoc}
   */
  protected function getTestIndexes(): array {
    return [self::TEST_INDEX];
  }

}
