<?php

namespace Drupal\Tests\search_api_meilisearch\Kernel\Api;

use <PERSON><PERSON><PERSON>\search_api_meilisearch\Api\MeilisearchApiException;
use Drupal\Tests\search_api_meilisearch\Kernel\MeilisearchKernelTestBase;
use <PERSON><PERSON><PERSON><PERSON>\Contracts\KeysResults;

/**
 * Provides keys api tests.
 *
 * @group search_api_meilisearch
 */
class KeyTest extends MeilisearchKernelTestBase {

  /**
   * Tests retrieving keys.
   */
  public function testRetrievingKeys(): void {
    $keys = $this->apiService->keys();
    $this->assertInstanceOf(KeysResults::class, $keys);
  }

  /**
   * Tests keys api on invalid connection.
   */
  public function testRetrievingKeysOnInvalidConnection(): void {
    $this->apiService->setUrl('http://non-existing-connection');
    $this->expectException(MeilisearchApiException::class);
    $this->apiService->keys();
  }

  /**
   * {@inheritdoc}
   */
  protected function getTestIndexes(): array {
    return [];
  }

}
