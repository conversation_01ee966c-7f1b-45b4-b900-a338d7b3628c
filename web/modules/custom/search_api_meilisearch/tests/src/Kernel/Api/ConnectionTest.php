<?php

namespace Drupal\Tests\search_api_meilisearch\Kernel\Api;

use <PERSON><PERSON><PERSON>\search_api_meilisearch\Api\MeilisearchApiException;
use Drupal\Tests\search_api_meilisearch\Kernel\MeilisearchKernelTestBase;
use <PERSON><PERSON>ear<PERSON>\Client;

/**
 * Performs connection tests.
 *
 * @group search_api_meilisearch
 */
class ConnectionTest extends MeilisearchKernelTestBase {

  /**
   * Tests that a valid connection returns a client.
   */
  public function testSuccessfulConnection(): void {
    $client = $this->apiService->connection();
    $this->assertInstanceOf(Client::class, $client);
    $this->assertTrue($this->apiService->ping());
  }

  /**
   * Tests that an exception is thrown on invalid url.
   *
   * @param string $url
   *   The url of the meilisearch instance.
   *
   * @dataProvider invalidUrlProvider
   */
  public function testConnectionExceptionThrownOnInvalidUrl(string $url): void {
    $this->expectException(MeilisearchApiException::class);
    $this->apiService->setUrl($url);
    $this->apiService->connection();
    $this->assertFalse($this->apiService->ping());
  }

  /**
   * Tests that an exception is thrown on invalid master key.
   *
   * @param string $masterKey
   *   The meilisearch master key.
   *
   * @dataProvider invalidMasterKeyProvider
   */
  public function testConnectionExceptionThrownOnInvalidMasterKey(string $masterKey): void {
    $this->expectException(MeilisearchApiException::class);
    $this->apiService->setMasterKey($masterKey);
    $this->apiService->connection();
    $this->assertFalse($this->apiService->ping());
  }

  /**
   * Provides invalid urls for tests.
   */
  public function invalidUrlProvider(): array {
    return [
      'empty url' => [''],
      'url without meilisearch instance' => ['http://example.com'],
    ];
  }

  /**
   * Provides invalid master keys for tests.
   */
  public function invalidMasterKeyProvider(): array {
    return [
      'empty master key' => [''],
      'invalid master key' => ['___'],
    ];
  }

  /**
   * {@inheritdoc}
   */
  protected function getTestIndexes(): array {
    return [];
  }

}
