<?php

namespace Drupal\Tests\search_api_meilisearch\Kernel\Api;

use <PERSON><PERSON>al\search_api_meilisearch\Api\MeilisearchApiException;
use Drupal\Tests\search_api_meilisearch\Kernel\MeilisearchKernelTestBase;
use Drupal\Tests\search_api_meilisearch\Traits\Api\DocumentsCreationTrait;
use Drupal\Tests\search_api_meilisearch\Traits\Api\IndexCreationTrait;

/**
 * Provides search tests.
 *
 * @group search_api_meilisearch
 */
class SearchTest extends MeilisearchKernelTestBase {

  use IndexCreationTrait;
  use DocumentsCreationTrait;

  protected const TEST_INDEX = 'kernel_search_test_index';

  /**
   * {@inheritdoc}
   */
  protected function setUp(): void {
    parent::setUp();

    $documents = [
      [
        'id' => 'entity_node_8_en',
        'title' => 'Die Hard',
        'body' => '<PERSON> tells the story of a New York City detective named <PERSON> who is separated from his wife. She invites him to a Christmas party at her office.',
        'status' => FALSE,
      ],
      [
        'id' => 'entity_node_9_en',
        'title' => 'The Matrix',
        'body' => '<PERSON>, a computer programmer, is led to fight an underground war against powerful computers who have constructed his entire reality with a system called the Matrix.',
        'status' => TRUE,
      ],
      [
        'id' => 'entity_node_10_en',
        'title' => 'Clerks',
        'body' => 'Two store clerks, Dante and Randal, trouble customers, discuss movies, laze around and play hockey on the store roof all the time.',
        'status' => TRUE,
      ],
    ];

    $this->createIndex(self::TEST_INDEX, $this->apiService);
    $this->createDocuments(self::TEST_INDEX, $documents, $this->apiService);
  }

  /**
   * Tests searching on an index without filters.
   */
  public function testSearchWithoutFilters(): void {
    $results = $this->apiService->search(self::TEST_INDEX, '');
    $this->assertCount(3, $results);

    $results = $this->apiService->search(self::TEST_INDEX, 'his');
    $this->assertCount(2, $results);
    $this->assertEquals(2, $results->getHitsCount());
    $this->assertEquals([
      [
        'id' => 'entity_node_8_en',
        'title' => 'Die Hard',
        'body' => 'Die Hard tells the story of a New York City detective named John McClane who is separated from his wife. She invites him to a Christmas party at her office.',
        'status' => FALSE,
      ],
      [
        'id' => 'entity_node_9_en',
        'title' => 'The Matrix',
        'body' => 'Thomas Anderson, a computer programmer, is led to fight an underground war against powerful computers who have constructed his entire reality with a system called the Matrix.',
        'status' => TRUE,
      ],
    ], $results->getHits());
  }

  /**
   * Tests searching on an index with filters without configured filters.
   */
  public function testSearchWithFiltersAndNoConfigurableFiltersSet() {
    $this->expectException(MeilisearchApiException::class);
    $this->apiService->search(self::TEST_INDEX, '', ['filter' => 'status = false']);
  }

  /**
   * Tests searching on an index with filters with configured filters.
   */
  public function testSearchWithFiltersAndConfigurableFiltersSet() {
    $task = $this->apiService->setFilterableAttributes(self::TEST_INDEX, ['status']);
    $this->apiService->waitForUpdate($task['taskUid']);
    $results = $this->apiService->search(self::TEST_INDEX, '', ['filter' => 'status = false']);
    $this->assertCount(1, $results);
    $this->assertEquals(1, $results->getHitsCount());
    $this->assertEquals([
      [
        'id' => 'entity_node_8_en',
        'title' => 'Die Hard',
        'body' => 'Die Hard tells the story of a New York City detective named John McClane who is separated from his wife. She invites him to a Christmas party at her office.',
        'status' => FALSE,
      ],
    ], $results->getHits());
  }

  /**
   * {@inheritdoc}
   */
  protected function getTestIndexes(): array {
    return [self::TEST_INDEX];
  }

}
