<?php

namespace Drupal\Tests\search_api_meilisearch\Kernel\Api;

use <PERSON><PERSON><PERSON>\search_api_meilisearch\Api\MeilisearchApiException;
use Drupal\Tests\search_api_meilisearch\Kernel\MeilisearchKernelTestBase;
use Drupal\Tests\search_api_meilisearch\Traits\Api\IndexCreationTrait;
use Meilisearch\Endpoints\Indexes;

/**
 * Provides index api tests.
 *
 * @group search_api_meilisearch
 */
class IndexTest extends MeilisearchKernelTestBase {

  use IndexCreationTrait;

  protected const TEST_INDEX = 'kernel_index_test_index';

  /**
   * Tests index creation and retrieval.
   */
  public function testCreateAndGetIndex(): void {
    $this->createIndex(self::TEST_INDEX, $this->apiService);
    $index = $this->apiService->getIndex(self::TEST_INDEX);
    $this->assertInstanceOf(Indexes::class, $index);
  }

  /**
   * Tests index removal.
   */
  public function testRemoveIndex(): void {
    $this->createIndex(self::TEST_INDEX, $this->apiService);
    $this->assertTrue($this->apiService->removeIndex(self::TEST_INDEX));
  }

  /**
   * Tests removing index that does not exist.
   */
  public function testRemovingNonexistingIndex(): void {
    $this->assertFalse($this->apiService->removeIndex(self::TEST_INDEX));
  }

  /**
   * Tests retrieving all indexes.
   */
  public function testListingAllIndexes(): void {
    $this->createIndex(self::TEST_INDEX, $this->apiService);
    $this->createIndex(self::TEST_INDEX . '_1', $this->apiService);
    $indexes = $this->apiService->listIndexes();

    // Instance may already contain non-test indexes so assertion is needed for
    // test indexes in results.
    $this->assertGreaterThanOrEqual(2, $indexes->count());
    $this->assertContainsOnlyInstancesOf(Indexes::class, $indexes);
    $testIndexFound = FALSE;
    $testIndex1Found = FALSE;
    /** @var \MeiliSearch\Endpoints\Indexes $index */
    foreach ($indexes as $index) {
      if ($index->getUid() === self::TEST_INDEX) {
        $testIndexFound = TRUE;
        continue;
      }

      if ($index->getUid() === self::TEST_INDEX . '_1') {
        $testIndex1Found = TRUE;
      }
    }

    if (!$testIndexFound || !$testIndex1Found) {
      $this->fail();
    }
  }

  /**
   * Tests endpoints on invalid connection.
   */
  public function testIndexEndpointsOnInvalidConnection(): void {
    $this->apiService->setUrl('http://non-existing-connection');
    $this->expectException(MeilisearchApiException::class);
    $this->createIndex(self::TEST_INDEX, $this->apiService);
    $this->apiService->getIndex(self::TEST_INDEX);
    $this->apiService->removeIndex(self::TEST_INDEX);
  }

  /**
   * {@inheritdoc}
   */
  protected function getTestIndexes(): array {
    return [
      self::TEST_INDEX,
      self::TEST_INDEX . '_1',
    ];
  }

}
