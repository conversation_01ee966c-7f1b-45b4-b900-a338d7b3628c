<?php

namespace Drupal\Tests\search_api_meilisearch\Kernel\Api;

use <PERSON><PERSON>al\search_api_meilisearch\Api\MeilisearchApiException;
use Drupal\Tests\search_api_meilisearch\Kernel\MeilisearchKernelTestBase;
use Drupal\Tests\search_api_meilisearch\Traits\Api\IndexCreationTrait;

/**
 * Provides settings api tests.
 *
 * @group search_api_meilisearch
 */
class SettingsTest extends MeilisearchKernelTestBase {

  use IndexCreationTrait;

  protected const TEST_INDEX = 'kernel_settings_test_index';

  /**
   * Tests setting and getting filterable attributes on an index.
   */
  public function testFilterSettings(): void {
    $this->createIndex(self::TEST_INDEX, $this->apiService);
    $setFilterableAttributes = $this->apiService->setFilterableAttributes(self::TEST_INDEX, ['body']);
    $task = $this->apiService->waitForUpdate($setFilterableAttributes['taskUid']);
    $this->assertArrayHasKey('status', $task);

    $settings = $this->apiService->getSettings(self::TEST_INDEX);
    $this->assertArrayHasKey('filterableAttributes', $settings);
    $this->assertEquals(['body'], $settings['filterableAttributes']);
  }

  /**
   * Tests setting, getting and resetting synonyms on an index.
   */
  public function testSynonymsSettings(): void {
    $this->createIndex(self::TEST_INDEX, $this->apiService);
    $synonyms = [
      'wow' => ['world of warcraft'],
    ];

    $task = $this->apiService->updateSynonyms(self::TEST_INDEX, $synonyms);
    $this->apiService->waitForUpdate($task['taskUid']);
    $this->assertEquals($synonyms, $this->apiService->getSynonyms(self::TEST_INDEX));

    $task = $this->apiService->resetSynonyms(self::TEST_INDEX);
    $this->apiService->waitForUpdate($task['taskUid']);
    $this->assertEquals([], $this->apiService->getSynonyms(self::TEST_INDEX));
  }

  /**
   * Tests setting, getting and resetting stop words on an index.
   */
  public function testStopwordsSettings(): void {
    $this->createIndex(self::TEST_INDEX, $this->apiService);
    $stopWords = ['a', 'an', 'the'];

    $task = $this->apiService->updateStopWords(self::TEST_INDEX, $stopWords);
    $this->apiService->waitForUpdate($task['taskUid']);
    $settings = $this->apiService->getSettings(self::TEST_INDEX);
    $this->assertArrayHasKey('stopWords', $settings);
    $this->assertEquals($stopWords, $settings['stopWords']);

    $task = $this->apiService->resetStopWords(self::TEST_INDEX);
    $this->apiService->waitForUpdate($task['taskUid']);
    $settings = $this->apiService->getSettings(self::TEST_INDEX);
    $this->assertArrayHasKey('stopWords', $settings);
    $this->assertEquals([], $settings['stopWords']);
  }

  /**
   * Tests settings endpoints on invalid connection.
   */
  public function testSettingsEndpointsOnInvalidConnection(): void {
    $this->apiService->setUrl('http://non-existing-connection');
    $this->expectException(MeilisearchApiException::class);
    $this->createIndex(self::TEST_INDEX, $this->apiService);
    $this->apiService->setFilterableAttributes(self::TEST_INDEX, ['body']);
    $this->apiService->waitForUpdate(1);
    $this->apiService->getFilterableAttributes(self::TEST_INDEX);
    $this->apiService->getSynonyms(self::TEST_INDEX);
    $this->apiService->updateSynonyms(self::TEST_INDEX, []);
    $this->apiService->resetSynonyms(self::TEST_INDEX);
    $this->apiService->updateStopWords(self::TEST_INDEX, []);
    $this->apiService->resetStopWords(self::TEST_INDEX);
  }

  /**
   * Tests setting and getting ranking rules on an index.
   */
  public function testRankingRules(): void {
    $this->createIndex(self::TEST_INDEX, $this->apiService);
    $rankingRules = ['sort', 'typo', 'words', 'proximity', 'exactness'];

    // Set ranking rules.
    $task = $this->apiService->setRankingRules(self::TEST_INDEX, $rankingRules);
    $this->apiService->waitForUpdate($task['taskUid']);
    $rules = $this->apiService->getRankingRules(self::TEST_INDEX);
    $this->assertEquals($rankingRules, $rules);
  }

  /**
   * Tests setting and getting searchable attributes on an index.
   */
  public function testSortableAttributes(): void {
    $this->createIndex(self::TEST_INDEX, $this->apiService);
    $sortableAttributes = ['title', 'body'];

    // Set searchable attributes.
    $task = $this->apiService->setSortableAttributes(self::TEST_INDEX, $sortableAttributes);
    $this->apiService->waitForUpdate($task['taskUid']);
    $attributes = $this->apiService->getSortableAttributes(self::TEST_INDEX);
    // Sort attributes to make sure they are in the same order.
    sort($sortableAttributes);
    sort($attributes);
    $this->assertEquals($sortableAttributes, $attributes);
  }

  /**
   * Tests setting and getting searchable attributes on an index.
   */
  public function testSearchableAttributes(): void {
    $this->createIndex(self::TEST_INDEX, $this->apiService);
    $searchableAttributes = ['title', 'body'];

    // Set searchable attributes.
    $task = $this->apiService->setSearchableAttributes(self::TEST_INDEX, $searchableAttributes);
    $this->apiService->waitForUpdate($task['taskUid']);
    $attributes = $this->apiService->getSearchableAttributes(self::TEST_INDEX);
    $this->assertEquals($searchableAttributes, $attributes);
  }

  /**
   * {@inheritdoc}
   */
  protected function getTestIndexes(): array {
    return [self::TEST_INDEX];
  }

}
