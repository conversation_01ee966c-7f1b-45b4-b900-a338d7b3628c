<?php

namespace Drupal\Tests\search_api_meilisearch\Kernel\Api;

use <PERSON><PERSON>al\search_api_meilisearch\Api\MeilisearchApiException;
use Drupal\Tests\search_api_meilisearch\Kernel\MeilisearchKernelTestBase;
use Drupal\Tests\search_api_meilisearch\Traits\Api\DocumentsCreationTrait;
use Drupal\Tests\search_api_meilisearch\Traits\Api\IndexCreationTrait;
use Meilisearch\Contracts\DocumentsQuery;

/**
 * Provides documents api tests.
 *
 * @group search_api_meilisearch
 */
class DocumentTest extends MeilisearchKernelTestBase {

  use IndexCreationTrait;
  use DocumentsCreationTrait;

  protected const TEST_INDEX = 'kernel_document_test_index';

  /**
   * Tests document creation and retrieval.
   */
  public function testCreateAndGetDocument(): void {
    $this->createIndex(self::TEST_INDEX, $this->apiService);
    $this->createDocuments(self::TEST_INDEX, [
      [
        'id' => 'entity_node_8_en',
        'title' => 'Die Hard',
        'body' => '<PERSON> tells the story of a New York City detective named <PERSON> Mc<PERSON>lane who is separated from his wife. She invites him to a Christmas party at her office.',
        'status' => TRUE,
      ],
    ], $this->apiService);
    $documents = $this->apiService->getDocument(self::TEST_INDEX, 'entity_node_8_en');
    $this->assertEquals([
      'id' => 'entity_node_8_en',
      'title' => 'Die Hard',
      'body' => 'Die Hard tells the story of a New York City detective named John McClane who is separated from his wife. She invites him to a Christmas party at her office.',
      'status' => TRUE,
    ], $documents);
  }

  /**
   * Tests document deletion.
   */
  public function testDeleteDocument(): void {
    $this->createIndex(self::TEST_INDEX, $this->apiService);
    $this->createDocuments(self::TEST_INDEX, [
      [
        'id' => 'entity_node_8_en',
        'title' => 'Die Hard',
        'body' => 'Die Hard tells the story of a New York City detective named John McClane who is separated from his wife. She invites him to a Christmas party at her office.',
        'status' => TRUE,
      ],
    ], $this->apiService);
    $task = $this->apiService->deleteDocument(self::TEST_INDEX, 'entity_node_8_en');
    $this->apiService->connection()->waitForTask($task['taskUid']);
    $documents = $this->apiService->getDocuments(self::TEST_INDEX);
    $this->assertCount(0, $documents);
  }

  /**
   * Tests deleting multiple documents.
   */
  public function testDeleteMultipleDocuments(): void {
    $testDocuments = [];
    foreach ([1, 2, 3] as $id) {
      $testDocuments[] = [
        'id' => "entity_node_{$id}_en",
        'title' => 'Die Hard',
        'body' => 'Die Hard tells the story of a New York City detective named John McClane who is separated from his wife. She invites him to a Christmas party at her office.',
        'status' => TRUE,
      ];
    }
    $this->createIndex(self::TEST_INDEX, $this->apiService);
    $this->createDocuments(self::TEST_INDEX, $testDocuments, $this->apiService);

    $task = $this->apiService->deleteDocuments(self::TEST_INDEX, [
      'entity_node_1_en',
      'entity_node_3_en',
    ]);
    $this->apiService->connection()->waitForTask($task['taskUid']);
    $documents = $this->apiService->getDocuments(self::TEST_INDEX);
    $this->assertCount(1, $documents);
    $this->assertEquals('entity_node_2_en', $documents[0]['id']);
  }

  /**
   * Tests deleting all documents.
   */
  public function testDeleteAllDocuments(): void {
    $testDocuments = [];
    foreach ([1, 2, 3] as $id) {
      $testDocuments[] = [
        'id' => "entity_node_{$id}_en",
        'title' => 'Die Hard',
        'body' => 'Die Hard tells the story of a New York City detective named John McClane who is separated from his wife. She invites him to a Christmas party at her office.',
        'status' => TRUE,
      ];
    }
    $this->createIndex(self::TEST_INDEX, $this->apiService);
    $this->createDocuments(self::TEST_INDEX, $testDocuments, $this->apiService);
    $task = $this->apiService->deleteAllDocuments(self::TEST_INDEX);
    $this->apiService->connection()->waitForTask($task['taskUid']);

    $documents = $this->apiService->getDocuments(self::TEST_INDEX);
    $this->assertCount(0, $documents);
  }

  /**
   * Tests retrieving multiple documents.
   */
  public function testRetrievingMultipleDocuments(): void {
    $testDocuments = [];
    foreach ([1, 2, 3] as $id) {
      $testDocuments[] = [
        'id' => "entity_node_{$id}_en",
        'title' => 'Die Hard',
        'body' => 'Die Hard tells the story of a New York City detective named John McClane who is separated from his wife. She invites him to a Christmas party at her office.',
        'status' => TRUE,
      ];
    }
    $this->createIndex(self::TEST_INDEX, $this->apiService);
    $this->createDocuments(self::TEST_INDEX, $testDocuments, $this->apiService);

    $documents = $this->apiService->getDocuments(self::TEST_INDEX);
    $this->assertCount(3, $documents);

    $query = new DocumentsQuery();
    $query->setLimit(2);

    $documents = $this->apiService->getDocuments(self::TEST_INDEX, $query);
    $this->assertCount(2, $documents);
  }

  /**
   * Tests endpoints on invalid connection.
   */
  public function testDocumentEndpointsOnInvalidRequest(): void {
    $this->apiService->setUrl('http://non-existing-connection');
    $this->expectException(MeilisearchApiException::class);
    $this->createDocuments(self::TEST_INDEX, [], $this->apiService);
    $this->apiService->getDocument(self::TEST_INDEX, 'entity_node_8_en');
    $this->apiService->getDocuments(self::TEST_INDEX);
    $this->apiService->deleteDocuments(self::TEST_INDEX, []);
    $this->apiService->deleteAllDocuments(self::TEST_INDEX);
  }

  /**
   * {@inheritdoc}
   */
  protected function getTestIndexes(): array {
    return [self::TEST_INDEX];
  }

}
