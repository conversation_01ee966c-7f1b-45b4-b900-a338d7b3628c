<?php

namespace Drupal\Tests\search_api_meilisearch\Kernel\Backend;

use Drupal\Core\Logger\LoggerChannel;
use Drupal\entity_test\Entity\EntityTest;
use Drupal\search_api\SearchApiException;

/**
 * Tests sorting when querying the search api index.
 *
 * @group search_api_meilisearch
 */
class SortingTest extends BackendKernelTestBase {

  protected const TEST_INDEX = 'kernel_search_sorting_test_index';

  /**
   * Tests various sorting scenarios.
   */
  public function testSearchSorting(): void {
    $this->prepareTestIndex(self::TEST_INDEX);
    $this->prepareTestItems();

    // Tests search without sorting.
    $results = $this->index->query()
      ->range(0, 10)
      ->execute();

    $this->assertCount(3, $results->getResultItems());

    $expected = [
      'entity:entity_test/1:en',
      'entity:entity_test/2:en',
      'entity:entity_test/3:en',
    ];
    $this->assertEquals($expected, array_keys($results->getResultItems()));

    // Tests search with DESC sorting.
    $results = $this->index->query()
      ->sort('name', 'DESC')
      ->range(0, 10)
      ->execute();

    $this->assertCount(3, $results->getResultItems());

    $expected = [
      'entity:entity_test/3:en',
      'entity:entity_test/2:en',
      'entity:entity_test/1:en',
    ];
    $this->assertEquals($expected, array_keys($results->getResultItems()));
  }

  /**
   * Tests throws exception when sorting by a non-existing field.
   */
  public function testSearchWithSortingAndNonExistingField(): void {
    $mockLogger = $this->createMock(LoggerChannel::class);
    $mockLogger->expects($this->once())->method('error');
    $this->container->set('logger.channel.search_api_meilisearch', $mockLogger);

    $this->prepareTestIndex(self::TEST_INDEX);
    $this->prepareTestItems();

    $results = $this->index->query()
      ->sort('non_existing_field', 'DESC')
      ->range(0, 10);

    $this->expectException(SearchApiException::class);
    $results->execute();
  }

  /**
   * Prepares search api index with items for tests.
   */
  protected function prepareTestItems(): void {
    foreach (['bar', 'baz', 'foo'] as $name) {
      EntityTest::create([
        'name' => $name,
      ])->save();
    }

    $this->index->indexItems();
  }

  /**
   * {@inheritdoc}
   */
  protected function getTestIndexes(): array {
    return [self::TEST_INDEX];
  }

}
