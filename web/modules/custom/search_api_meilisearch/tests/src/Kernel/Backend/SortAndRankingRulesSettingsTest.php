<?php

namespace Drupal\Tests\search_api_meilisearch\Kernel\Backend;

use Drupal\search_api\Item\Field;

/**
 * Provides tests for sorting and ranking rules settings.
 *
 * @group search_api_meilisearch
 */
class SortAndRankingRulesSettingsTest extends BackendKernelTestBase {

  protected const TEST_INDEX = 'kernel_sort_ranking_test_index';

  /**
   * Tests sortable attributes and ranking rules on index operations.
   */
  public function testRankingRulesAndSortingAttributesAreSet(): void {
    $this->prepareTestIndex(self::TEST_INDEX);

    // Tests ranking and sorting settings are set after index creation.
    $defaultRankingRules = [
      'sort',
      'words',
      'attribute',
      'typo',
      'proximity',
      'exactness',
    ];
    $settings = $this->apiService->getRankingRules(self::TEST_INDEX);
    $this->assertEquals($defaultRankingRules, $settings);

    $defaultSortingAttributes = [
      'body',
      'name',
      'search_api_datasource',
      'search_api_id',
      'search_api_language',
      'weight',
    ];

    $settings = $this->apiService->getSortableAttributes(self::TEST_INDEX);
    $this->assertEquals($defaultSortingAttributes, $settings);

    // Tests that sorting settings are set after adding a field to index.
    $idField = new Field($this->index, 'created');
    $idField->setType('integer')
      ->setDatasourceId('entity:entity_test')
      ->setPropertyPath('created');

    $this->index
      ->addField($idField)
      ->save();

    $defaultSortingAttributes = [
      'body',
      'created',
      'name',
      'search_api_datasource',
      'search_api_id',
      'search_api_language',
      'weight',
    ];

    $settings = $this->apiService->getSortableAttributes(self::TEST_INDEX);
    $this->assertEquals($defaultSortingAttributes, $settings);

    $this->index
      ->removeField('created')
      ->save();

    $defaultSortingAttributes = [
      'body',
      'name',
      'search_api_datasource',
      'search_api_id',
      'search_api_language',
      'weight',
    ];
    $settings = $this->apiService->getSortableAttributes(self::TEST_INDEX);
    $this->assertEquals($defaultSortingAttributes, $settings);
  }

  /**
   * {@inheritdoc}
   */
  protected function getTestIndexes(): array {
    return [self::TEST_INDEX];
  }

}
