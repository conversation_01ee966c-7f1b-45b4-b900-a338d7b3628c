<?php

/**
 * @file
 * Script pour importer des traductions de taxonomie.
 *
 * Utilisation:
 * drush php:script web/modules/custom/import_reglementation/scripts/import_taxonomy_translations.php -- [fichier_csv] [vocabulaire] [délimiteur] [colonne_fr] [colonne_ar]
 *
 * Exemple:
 * drush php:script web/modules/custom/import_reglementation/scripts/import_taxonomy_translations.php -- web/modules/custom/import_reglementation/csv/taxonomies_translations.csv type , fr ar
 */

use Drupal\taxonomy\Entity\Term;

// Vérifier les arguments
if (count($extra) < 2) {
  echo "Usage: drush php:script import_taxonomy_translations.php -- [fichier_csv] [vocabulaire] [délimiteur] [colonne_fr] [colonne_ar]\n";
  exit(1);
}

$file = $extra[0];
$vocabulary = $extra[1];
$delimiter = isset($extra[2]) ? $extra[2] : ',';
$fr_column = isset($extra[3]) ? $extra[3] : 'fr';
$ar_column = isset($extra[4]) ? $extra[4] : 'ar';

// Vérifier si le fichier existe
if (!file_exists($file)) {
  echo "Erreur: Le fichier $file n'existe pas.\n";
  exit(1);
}

// Vérifier si le vocabulaire existe
$vocabularies = \Drupal::entityTypeManager()
  ->getStorage('taxonomy_vocabulary')
  ->loadByProperties(['vid' => $vocabulary]);

if (empty($vocabularies)) {
  $vids = implode(', ', array_keys(\Drupal::entityTypeManager()->getStorage('taxonomy_vocabulary')->loadMultiple()));
  echo "Erreur: Le vocabulaire $vocabulary n'existe pas. Vocabulaires disponibles: $vids\n";
  exit(1);
}

// Ouvrir le fichier CSV
if (($handle = fopen($file, 'r')) !== FALSE) {
  // Lire l'en-tête pour obtenir les noms de colonnes
  $header = fgetcsv($handle, 0, $delimiter);
  
  // Vérifier que les colonnes requises sont présentes
  if (!in_array($fr_column, $header) || !in_array($ar_column, $header)) {
    echo "Erreur: Les colonnes $fr_column et/ou $ar_column sont manquantes dans le fichier CSV.\n";
    fclose($handle);
    exit(1);
  }
  
  $count = 0;
  $errors = 0;
  
  // Traiter chaque ligne du CSV
  while (($data = fgetcsv($handle, 0, $delimiter)) !== FALSE) {
    $row = array_combine($header, $data);
    
    if (empty($row[$fr_column]) || empty($row[$ar_column])) {
      echo "Avertissement: Ligne ignorée: valeurs manquantes.\n";
      continue;
    }
    
    // Rechercher le terme en français
    $terms = \Drupal::entityTypeManager()
      ->getStorage('taxonomy_term')
      ->loadByProperties([
        'name' => $row[$fr_column],
        'vid' => $vocabulary,
      ]);
    
    if (empty($terms)) {
      echo "Avertissement: Terme non trouvé: {$row[$fr_column]}\n";
      $errors++;
      continue;
    }
    
    $term = reset($terms);
    
    try {
      // Vérifier si le terme a déjà une traduction en arabe
      if ($term->hasTranslation('ar')) {
        $translation = $term->getTranslation('ar');
      } 
      else {
        // Créer une nouvelle traduction
        $translation = $term->addTranslation('ar');
      }
      
      // Définir le nom en arabe
      $translation->setName($row[$ar_column]);
      
      // Sauvegarder la traduction
      $translation->save();
      
      $count++;
      echo "Traduction ajoutée pour le terme \"{$row[$fr_column]}\": {$row[$ar_column]}\n";
    }
    catch (\Exception $e) {
      echo "Erreur lors de l'ajout de la traduction pour le terme \"{$row[$fr_column]}\": {$e->getMessage()}\n";
      $errors++;
    }
  }
  
  fclose($handle);
  
  echo "$count traductions ajoutées avec succès. $errors erreurs rencontrées.\n";
}
else {
  echo "Erreur: Impossible d'ouvrir le fichier $file.\n";
  exit(1);
}
