<?php

/**
 * Script de test pour l'importation CSV
 */

use Drupal\import_reglementation\Service\CsvImporter;

// Charger Drupal
$autoloader = require_once 'vendor/autoload.php';
\Drupal\Core\DrupalKernel::createFromRequest(\Symfony\Component\HttpFoundation\Request::createFromGlobals())->boot();

// Obtenir le service d'importation
$importer = \Drupal::service('import_reglementation.csv_importer');

// Chemin vers le fichier CSV
$csv_file = 'web/modules/custom/import_reglementation/csv/Réglementation_MTL - Transport Routier (2).csv';

echo "Début du test d'importation...\n";
echo "Fichier CSV: $csv_file\n";

// Tester l'importation
$results = $importer->import($csv_file, ',');

echo "Résultats de l'importation:\n";
echo "- Succès: " . ($results['success'] ? 'Oui' : 'Non') . "\n";
echo "- Créés: " . $results['created'] . "\n";
echo "- Mis à jour: " . $results['updated'] . "\n";
echo "- Traités: " . $results['processed'] . "\n";
echo "- Erreurs: " . count($results['errors']) . "\n";

if (!empty($results['errors'])) {
  echo "\nErreurs détaillées:\n";
  foreach ($results['errors'] as $error) {
    echo "- $error\n";
  }
}

echo "\nTest terminé.\n";
