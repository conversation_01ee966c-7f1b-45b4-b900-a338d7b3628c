<?php

/**
 * @file
 * Drush command pour importer des traductions de taxonomie.
 */

use Drupal\taxonomy\Entity\Term;
use Symfony\Component\Yaml\Yaml;

/**
 * Implements hook_drush_command().
 */
function import_taxonomy_translations_drush_command() {
  $items = [];
  $items['import-taxonomy-translations'] = [
    'description' => 'Importe des traductions de taxonomie à partir d\'un fichier CSV.',
    'arguments' => [
      'file' => 'Chemin vers le fichier CSV contenant les traductions.',
      'vocabulary' => 'ID du vocabulaire à traduire.',
      'delimiter' => 'Délimiteur utilisé dans le CSV (par défaut: ,).',
    ],
    'options' => [
      'fr-column' => 'Nom de la colonne contenant les termes en français (par défaut: fr).',
      'ar-column' => 'Nom de la colonne contenant les termes en arabe (par défaut: ar).',
    ],
    'examples' => [
      'drush import-taxonomy-translations /chemin/vers/fichier.csv type' => 'Importe les traductions pour le vocabulaire "type".',
      'drush import-taxonomy-translations /chemin/vers/fichier.csv type ";" --fr-column="Terme FR" --ar-column="Terme AR"' => 'Importe les traductions avec des paramètres personnalisés.',
    ],
    'aliases' => ['itt'],
  ];
  return $items;
}

/**
 * Callback pour la commande drush import-taxonomy-translations.
 */
function drush_import_taxonomy_translations($file = NULL, $vocabulary = NULL, $delimiter = ',') {
  if (!$file) {
    return drush_set_error('IMPORT_TAXONOMY_TRANSLATIONS_ERROR', dt('Veuillez spécifier un fichier CSV.'));
  }
  
  if (!$vocabulary) {
    return drush_set_error('IMPORT_TAXONOMY_TRANSLATIONS_ERROR', dt('Veuillez spécifier un vocabulaire.'));
  }
  
  // Vérifier si le fichier existe
  if (!file_exists($file)) {
    return drush_set_error('IMPORT_TAXONOMY_TRANSLATIONS_ERROR', dt('Le fichier @file n\'existe pas.', ['@file' => $file]));
  }
  
  // Vérifier si le vocabulaire existe
  $vocabularies = \Drupal::entityTypeManager()
    ->getStorage('taxonomy_vocabulary')
    ->loadByProperties(['vid' => $vocabulary]);
  
  if (empty($vocabularies)) {
    $vids = implode(', ', array_keys(\Drupal::entityTypeManager()->getStorage('taxonomy_vocabulary')->loadMultiple()));
    return drush_set_error('IMPORT_TAXONOMY_TRANSLATIONS_ERROR', dt('Le vocabulaire @vocabulary n\'existe pas. Vocabulaires disponibles: @vids', [
      '@vocabulary' => $vocabulary,
      '@vids' => $vids,
    ]));
  }
  
  // Récupérer les options
  $fr_column = drush_get_option('fr-column', 'fr');
  $ar_column = drush_get_option('ar-column', 'ar');
  
  // Ouvrir le fichier CSV
  if (($handle = fopen($file, 'r')) !== FALSE) {
    // Lire l'en-tête pour obtenir les noms de colonnes
    $header = fgetcsv($handle, 0, $delimiter);
    
    // Vérifier que les colonnes requises sont présentes
    if (!in_array($fr_column, $header) || !in_array($ar_column, $header)) {
      fclose($handle);
      return drush_set_error('IMPORT_TAXONOMY_TRANSLATIONS_ERROR', dt('Les colonnes @fr_column et/ou @ar_column sont manquantes dans le fichier CSV.', [
        '@fr_column' => $fr_column,
        '@ar_column' => $ar_column,
      ]));
    }
    
    $count = 0;
    $errors = 0;
    
    // Traiter chaque ligne du CSV
    while (($data = fgetcsv($handle, 0, $delimiter)) !== FALSE) {
      $row = array_combine($header, $data);
      
      if (empty($row[$fr_column]) || empty($row[$ar_column])) {
        drush_log(dt('Ligne ignorée: valeurs manquantes.'), 'warning');
        continue;
      }
      
      // Rechercher le terme en français
      $terms = \Drupal::entityTypeManager()
        ->getStorage('taxonomy_term')
        ->loadByProperties([
          'name' => $row[$fr_column],
          'vid' => $vocabulary,
          'langcode' => 'fr',
        ]);
      
      if (empty($terms)) {
        drush_log(dt('Terme non trouvé: @term', ['@term' => $row[$fr_column]]), 'warning');
        $errors++;
        continue;
      }
      
      $term = reset($terms);
      
      try {
        // Vérifier si le terme a déjà une traduction en arabe
        if ($term->hasTranslation('ar')) {
          $translation = $term->getTranslation('ar');
        } 
        else {
          // Créer une nouvelle traduction
          $translation = $term->addTranslation('ar');
        }
        
        // Définir le nom en arabe
        $translation->setName($row[$ar_column]);
        
        // Sauvegarder la traduction
        $translation->save();
        
        $count++;
        drush_log(dt('Traduction ajoutée pour le terme "@term": @translation', [
          '@term' => $row[$fr_column],
          '@translation' => $row[$ar_column],
        ]), 'success');
      }
      catch (\Exception $e) {
        drush_log(dt('Erreur lors de l\'ajout de la traduction pour le terme "@term": @error', [
          '@term' => $row[$fr_column],
          '@error' => $e->getMessage(),
        ]), 'error');
        $errors++;
      }
    }
    
    fclose($handle);
    
    drush_log(dt('@count traductions ajoutées avec succès. @errors erreurs rencontrées.', [
      '@count' => $count,
      '@errors' => $errors,
    ]), 'success');
  }
  else {
    return drush_set_error('IMPORT_TAXONOMY_TRANSLATIONS_ERROR', dt('Impossible d\'ouvrir le fichier @file.', ['@file' => $file]));
  }
}
