<?php

namespace Drush\Commands\import_reglementation;

use Drush\Commands\DrushCommands;
use <PERSON><PERSON>al\import_reglementation\Service\CsvImporter;

/**
 * Commandes Drush pour l'importation de réglementations.
 */
class ImportReglementationCommands extends DrushCommands {

  /**
   * Le service d'importation CSV.
   *
   * @var \Drupal\import_reglementation\Service\CsvImporter
   */
  protected $csvImporter;

  /**
   * Constructeur.
   *
   * @param \Drupal\import_reglementation\Service\CsvImporter $csv_importer
   *   Le service d'importation CSV.
   */
  public function __construct(CsvImporter $csv_importer) {
    $this->csvImporter = $csv_importer;
  }

  /**
   * Importe les réglementations depuis un fichier CSV.
   *
   * @param string $file_path
   *   Chemin vers le fichier CSV.
   * @param array $options
   *   Options de la commande.
   *
   * @command import-reglementation:csv
   * @aliases irc
   * @option delimiter Le délimiteur CSV (par défaut: ,)
   * @usage import-reglementation:csv /path/to/file.csv
   *   Importe les réglementations depuis le fichier CSV spécifié.
   */
  public function importCsv($file_path, array $options = ['delimiter' => ',']) {
    $delimiter = $options['delimiter'];
    
    $this->output()->writeln("Début de l'importation depuis: $file_path");
    $this->output()->writeln("Délimiteur: $delimiter");
    
    // Vérifier que le fichier existe
    if (!file_exists($file_path)) {
      $this->logger()->error("Le fichier $file_path n'existe pas.");
      return;
    }
    
    // Effectuer l'importation
    $results = $this->csvImporter->import($file_path, $delimiter);
    
    // Afficher les résultats
    $this->output()->writeln("Résultats de l'importation:");
    $this->output()->writeln("- Succès: " . ($results['success'] ? 'Oui' : 'Non'));
    $this->output()->writeln("- Créés: " . $results['created']);
    $this->output()->writeln("- Mis à jour: " . $results['updated']);
    $this->output()->writeln("- Traités: " . $results['processed']);
    $this->output()->writeln("- Erreurs: " . count($results['errors']));
    
    if (!empty($results['errors'])) {
      $this->output()->writeln("\nErreurs détaillées:");
      foreach ($results['errors'] as $error) {
        $this->logger()->error($error);
      }
    }
    
    if ($results['success']) {
      $this->logger()->success("Importation terminée avec succès!");
    } else {
      $this->logger()->error("L'importation a échoué.");
    }
  }
}
