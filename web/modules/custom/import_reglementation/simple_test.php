<?php

// Test simple de l'importation CSV
echo "=== Test d'importation CSV ===\n";

// Chemin vers le fichier CSV de test
$csv_file = __DIR__ . '/csv/test_small.csv';

echo "Fichier CSV: $csv_file\n";

// Vérifier que le fichier existe
if (!file_exists($csv_file)) {
    echo "ERREUR: Le fichier CSV n'existe pas!\n";
    exit(1);
}

// Lire le fichier CSV
$handle = fopen($csv_file, 'r');
if ($handle === FALSE) {
    echo "ERREUR: Impossible d'ouvrir le fichier CSV!\n";
    exit(1);
}

// Lire l'en-tête
$header = fgetcsv($handle, 0, ',');
echo "En-tête CSV: " . implode(' | ', $header) . "\n";

// Lire la première ligne de données
$data = fgetcsv($handle, 0, ',');
if ($data !== FALSE) {
    echo "Première ligne: " . implode(' | ', $data) . "\n";
    
    // Créer un tableau associatif
    $row = array_combine($header, $data);
    echo "Données structurées:\n";
    foreach ($row as $key => $value) {
        echo "  $key: $value\n";
    }
} else {
    echo "ERREUR: Aucune donnée trouvée!\n";
}

fclose($handle);

echo "\n=== Test terminé ===\n";
